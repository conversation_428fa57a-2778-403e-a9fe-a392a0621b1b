[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "llm-rag-codebase-query"
version = "0.1.0"
description = "Multi-agent RAG system for querying GitHub repository codebases"
readme = "README.md"
license = { file = "LICENSE" }
authors = [{ name = "LLM RAG Team", email = "<EMAIL>" }]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.13",
]
requires-python = ">=3.13"
dependencies = [
    # LLM & AI Orchestration
    "langchain>=0.3.0",
    "llama-index>=0.11.0",
    "transformers>=4.47.0",
    "openai>=1.58.0",
    "instructor>=1.8.0",
    "pydantic>=2.10.0",
    # Vector Databases
    "chromadb>=0.5.0",
    "weaviate-client>=4.9.0",
    # Data Processing
    "pandas>=2.2.0",
    "numpy>=1.26.0",
    "beautifulsoup4>=4.12.0",
    "pymupdf>=1.25.0",
    "pdfplumber>=0.11.0",
    # Web Framework & API
    "fastapi>=0.115.0",
    "uvicorn[standard]>=0.32.0",
    "httpx>=0.28.0",
    # Configuration & Environment
    "python-dotenv>=1.0.0",
    "pyyaml>=6.0.0",
    # GitHub Integration
    "pygithub>=2.5.0",
    "gitpython>=3.1.0",
    # Utilities
    "click>=8.1.0",
    "rich>=13.9.0",
    "loguru>=0.7.0",
    "pydantic-settings>=2.10.1",
    "tiktoken>=0.11.0",
    "chardet>=5.2.0",
    "markdown>=3.8.2",
]

[project.optional-dependencies]
dev = [
    # Testing
    "pytest>=8.3.0",
    "pytest-asyncio>=0.24.0",
    "pytest-cov>=6.0.0",
    "pytest-mock>=3.14.0",

    # Code Quality
    "black>=24.10.0",
    "ruff>=0.8.0",
    "mypy>=1.13.0",
    "pre-commit>=4.0.0",

    # Development Tools
    "ipython>=8.30.0",
    "jupyter>=1.1.0",

    # Security Tools
    "detect-secrets>=1.5.0",
    "bandit>=1.8.0",
    "safety>=3.2.0",
]

[project.scripts]
ragsys = "src.cli:main"

[tool.hatch.build.targets.wheel]
packages = ["src"]

[project.urls]
Homepage = "https://github.com/yourusername/llm-rag-codebase-query"
Repository = "https://github.com/yourusername/llm-rag-codebase-query"
Documentation = "https://github.com/yourusername/llm-rag-codebase-query/docs"
Issues = "https://github.com/yourusername/llm-rag-codebase-query/issues"

[tool.black]
line-length = 120
target-version = ['py313']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.ruff]
target-version = "py313"
line-length = 120

[tool.ruff.lint]
select = [
    "E",   # pycodestyle errors
    "W",   # pycodestyle warnings
    "F",   # pyflakes
    "I",   # isort
    "B",   # flake8-bugbear
    "C4",  # flake8-comprehensions
    "UP",  # pyupgrade
    "N",   # pep8-naming
    "S",   # flake8-bandit (security)
    "T20", # flake8-print
    "PT",  # flake8-pytest-style
    "RET", # flake8-return
    "SIM", # flake8-simplify
    "ARG", # flake8-unused-arguments
    "ERA", # eradicate (commented-out code)
    "PL",  # pylint
    "RUF", # ruff-specific rules
]
ignore = [
    "E501",    # line too long, handled by black
    "B008",    # do not perform function calls in argument defaults
    "C901",    # too complex
    "S101",    # use of assert
    "S104",    # possible binding to all interfaces
    "PLR0913", # too many arguments
    "PLR0915", # too many statements
    "PLR2004", # magic value used in comparison
]

[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["F401"]
"tests/**/*.py" = ["S101", "ARG001", "PLR2004"]
"src/config.py" = ["S105", "S106"]              # hardcoded passwords in config template

[tool.ruff.lint.isort]
known-first-party = ["src"]
force-sort-within-sections = true
split-on-trailing-comma = true

[tool.ruff.lint.mccabe]
max-complexity = 10

[tool.ruff.lint.pylint]
max-args = 8
max-branches = 12
max-returns = 6
max-statements = 50

[tool.mypy]
python_version = "3.13"
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
disallow_untyped_defs = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_return_any = true
strict_equality = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "e2e: marks tests as end-to-end tests",
]

[tool.coverage.run]
source = ["src"]
omit = ["*/tests/*", "*/test_*", "*/__pycache__/*"]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[dependency-groups]
dev = [
    "bandit>=1.8.6",
    "black>=25.1.0",
    "detect-secrets>=1.5.0",
    "pre-commit>=4.3.0",
    "pytest>=8.4.1",
    "pytest-asyncio>=1.1.0",
    "pytest-cov>=6.2.1",
    "pytest-mock>=3.14.1",
    "safety>=3.2.9",
]
