#!/usr/bin/env python3
"""
GitHub Connector Usage Example

This example demonstrates how to use the GitHub Connector to ingest
a repository and extract metadata from its files.
"""

import asyncio
import logging
from pathlib import Path

from src.config import get_settings
from src.ingestion.github import GitHubConnector

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def main():
    """Main example function."""
    # Get settings
    settings = get_settings()
    
    # Create GitHub connector
    connector = GitHubConnector(settings)
    
    try:
        # Example 1: Ingest a public repository
        logger.info("=== Example 1: Ingesting a public repository ===")
        
        repo_url = "https://github.com/octocat/Hello-World"
        result = await connector.ingest_repository(repo_url, branch="master")
        
        print(f"Repository: {result['repository_url']}")
        print(f"Branch: {result['branch']}")
        print(f"Total files found: {result['total_files']}")
        print(f"Files processed: {result['processed_files']}")
        print(f"Ingestion duration: {result['ingestion_duration']:.2f} seconds")
        
        # Show some file metadata
        print("\nSample file metadata:")
        for i, file_meta in enumerate(result['file_metadata'][:3]):  # Show first 3 files
            print(f"  {i+1}. {file_meta['file_path']}")
            print(f"     Type: {file_meta['file_type']}")
            print(f"     Size: {file_meta['file_size']} bytes")
            print(f"     Priority: {file_meta['priority']}")
            if file_meta.get('language'):
                print(f"     Language: {file_meta['language']}")
        
        # Example 2: Get ingestion status
        logger.info("\n=== Example 2: Checking ingestion status ===")
        
        status = await connector.get_ingestion_status(repo_url)
        print(f"Status: {status['status']}")
        print(f"Last updated: {status['last_updated']}")
        
        # Example 3: Get connector statistics
        logger.info("\n=== Example 3: Connector statistics ===")
        
        stats = connector.get_connector_stats()
        print(f"Total ingestions: {stats['total_ingestions']}")
        print(f"Max file size: {stats['settings']['max_file_size']} bytes")
        print(f"Allowed file types: {stats['settings']['allowed_file_types']}")
        
        filter_stats = stats['filter_stats']
        print(f"Exclusion patterns: {filter_stats['exclusion_count']}")
        print(f"Priority boosts: {filter_stats['priority_boost_count']}")
        
        # Example 4: Demonstrate file filtering
        logger.info("\n=== Example 4: File filtering demonstration ===")
        
        sample_files = [
            "README.md",           # Should be included with high priority
            "src/main.py",         # Should be included
            "docs/api.md",         # Should be included with high priority
            "node_modules/pkg.js", # Should be excluded
            "image.png",           # Should be excluded
            "tests/test.py",       # Should be included but lower priority
        ]
        
        filtered_files = connector.file_filter.filter_files(sample_files)
        print("Original files:", sample_files)
        print("Filtered files:", filtered_files)
        
        print("\nFile priorities:")
        for file_path in filtered_files:
            priority = connector.file_filter.get_file_priority(file_path)
            print(f"  {file_path}: {priority}")
        
    except Exception as e:
        logger.error(f"Error during ingestion: {e}")
        raise
    
    finally:
        # Clean up temporary files
        await connector.cleanup()
        logger.info("Cleanup completed")


async def advanced_example():
    """Advanced usage example with custom configuration."""
    logger.info("\n=== Advanced Example: Custom Configuration ===")
    
    settings = get_settings()
    
    # Create connector with custom exclusions
    from src.ingestion.github.filters import GitHubFileFilter
    
    custom_exclusions = {"*.backup", "temp/"}
    file_filter = GitHubFileFilter(settings, custom_exclusions)
    
    connector = GitHubConnector(
        settings=settings,
        file_filter=file_filter
    )
    
    try:
        # Demonstrate custom filtering
        test_files = [
            "main.py",
            "config.backup",  # Should be excluded by custom rule
            "temp/cache.txt", # Should be excluded by custom rule
            "docs/guide.md",
        ]
        
        filtered = connector.file_filter.filter_files(test_files)
        print("Files with custom exclusions:")
        print(f"  Original: {test_files}")
        print(f"  Filtered: {filtered}")
        
        # Add a new exclusion dynamically
        connector.file_filter.add_exclusion("*.tmp")
        
        test_files_2 = ["data.tmp", "script.py"]
        filtered_2 = connector.file_filter.filter_files(test_files_2)
        print(f"\nAfter adding *.tmp exclusion:")
        print(f"  Original: {test_files_2}")
        print(f"  Filtered: {filtered_2}")
        
    finally:
        await connector.cleanup()


if __name__ == "__main__":
    print("GitHub Connector Usage Example")
    print("=" * 50)
    
    # Run basic example
    asyncio.run(main())
    
    # Run advanced example
    asyncio.run(advanced_example())
    
    print("\nExample completed successfully!")
