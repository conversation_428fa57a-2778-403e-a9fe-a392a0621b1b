# LLM RAG Codebase Query System

A sophisticated multi-agent Retrieval-Augmented Generation (RAG) system designed to enable
intelligent querying of GitHub repository codebases. This system leverages specialized AI agents to
provide comprehensive analysis, design insights, and task planning for software development
projects.

## 🚀 Features

- **Multi-Agent Architecture**: Orchestrator, Technical Design Architect, and Task Planner agents
- **Advanced RAG Pipeline**: Intelligent document ingestion, retrieval, and ranking
- **GitHub Integration**: Seamless repository analysis and codebase understanding
- **Vector Database Support**: ChromaDB and Weaviate integration
- **Modern Tech Stack**: FastAPI backend, Next.js frontend, Docker containerization
- **Comprehensive Testing**: Unit, integration, and end-to-end test coverage
- **Code Quality**: Automated linting, formatting, and security checks

## 📋 Prerequisites

- **Python**: 3.13+
- **Node.js**: 20+
- **Docker**: Latest version
- **Git**: Latest version
- **uv**: Python package manager
- **pnpm**: Node.js package manager

## 📚 Project Guidelines

**IMPORTANT:** This project follows **strict guidelines** located in the `docs/` folder:

| File                                           | Purpose                                             |
| ---------------------------------------------- | --------------------------------------------------- |
| [`docs/structure.md`](docs/structure.md)       | Project structure & architecture                    |
| [`docs/tech.md`](docs/tech.md)                 | Technology stack & tool versions                    |
| [`docs/rules.md`](docs/rules.md)               | **Mandatory development standards & quality gates** |
| [`docs/requirements.md`](docs/requirements.md) | Functional & non-functional requirements            |
| [`docs/design.md`](docs/design.md)             | Technical architecture & design decisions           |
| [`docs/tasks.md`](docs/tasks.md)               | Task planning & implementation tracking             |

All development decisions **must** comply with these documents.

## 🛠️ Installation

### 1. Clone the Repository

```bash
git clone https://github.com/yourusername/llm-rag-codebase-query.git
cd llm-rag-codebase-query
```

### 2. Environment Setup

Create environment configuration files:

```bash
# Backend environment
cp .env.example .env
# Edit .env with your configuration

# Frontend environment
cp frontend/.env.example frontend/.env.local
# Edit frontend/.env.local with your configuration
```

### 3. Backend Setup

```bash
# Install uv (if not already installed)
curl -LsSf https://astral.sh/uv/install.sh | sh

# Navigate to backend and install dependencies
cd backend
uv sync

# Run backend development server
uv run python -m src.main
```

### 4. Frontend Setup

```bash
# Install pnpm (if not already installed)
npm install -g pnpm

# Navigate to frontend and install dependencies
cd frontend
pnpm install

# Run frontend development server
pnpm dev
```

### 5. Docker Setup (Recommended)

```bash
# Build and run with Docker Compose
docker-compose up --build

# For production
docker-compose -f docker-compose.prod.yml up --build
```

## 🧪 Development

### Code Quality

```bash
# Run linting
cd backend && uv run ruff check .
cd frontend && pnpm lint

# Run formatting
cd backend && uv run ruff format .
cd frontend && pnpm format

# Run type checking
cd frontend && pnpm type-check
```

### Testing

```bash
# Run backend tests
cd backend && uv run pytest

# Run frontend tests
cd frontend && pnpm test
```

## 🚀 Quick Start

### Development Mode

1. **Start Backend Services**:

   Start Redis and ChromaDB:

   ```bash
   docker-compose up -d redis chroma
   ```

   Start backend development server:

   ```bash
   uv run uvicorn src.main:app --reload --host 0.0.0.0 --port 8000
   ```

1. **Start Frontend**:

   ```bash
   cd frontend && pnpm run dev
   ```

1. **Access the Application**:

- Frontend: <http://localhost:3000>
- Backend API: <http://localhost:8000>
- API Documentation: <http://localhost:8000/docs>
- ChromaDB: <http://localhost:8001>

### Production Mode

```bash
# Build and start all services
docker-compose -f docker-compose.prod.yml up -d

# Access via Nginx reverse proxy
# Frontend: http://localhost
# Backend API: http://localhost/api
```

## 🧪 Testing

### Run All Tests

```bash
# Backend tests
uv run pytest

# Frontend tests
cd frontend && pnpm run test

# End-to-end tests
cd frontend && pnpm run test:e2e
```

### Code Quality Checks

```bash
# Python linting and formatting
uv run ruff check .
uv run ruff format .
uv run black .
uv run mypy src/

# JavaScript/TypeScript linting and formatting
cd frontend
pnpm run lint
pnpm run format
pnpm run type-check
```

### Pre-commit Hooks

```bash
# Install pre-commit hooks
uv run pre-commit install
uv run pre-commit install --hook-type commit-msg

# Run pre-commit on all files
uv run pre-commit run --all-files
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

See [CONTRIBUTING.md](CONTRIBUTING.md) for detailed guidelines.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

A sophisticated multi-agent Retrieval-Augmented Generation (RAG) system designed to enable
intelligent querying of GitHub repository codebases. This system leverages specialized AI agents to
provide comprehensive analysis, design insights, and task planning for software development
projects.

---

-
-
-
