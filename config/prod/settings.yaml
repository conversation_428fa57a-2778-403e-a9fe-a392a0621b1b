# Production Environment Configuration
# This file contains production-specific settings

application:
  name: "LLM RAG Codebase Query System"
  version: "0.1.0"
  environment: "production"
  debug: false

logging:
  level: "WARNING"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  handlers:
    - console
    - file
  file:
    path: "/var/log/rag-system.log"
    max_size: "100MB"
    backup_count: 5
  
api:
  host: "0.0.0.0"
  port: 8000
  workers: 4
  reload: false
  cors:
    enabled: true
    origins: []  # Set via environment variables

llm:
  provider: "openai"
  model: "gpt-4"
  temperature: 0.1
  max_tokens: 4000
  timeout: 60

embedding:
  model: "text-embedding-ada-002"
  dimension: 1536
  batch_size: 500

vector_db:
  provider: "chroma"
  host: "chroma"
  port: 8000
  collection: "codebase_embeddings_prod"
  
cache:
  provider: "redis"
  host: "redis"
  port: 6379
  db: 0
  ttl: 7200

github:
  api_url: "https://api.github.com"
  timeout: 60
  rate_limit:
    requests: 5000
    window: 3600

processing:
  chunk_size: 1000
  chunk_overlap: 200
  max_file_size: 52428800  # 50MB
  allowed_extensions:
    - ".py"
    - ".js"
    - ".ts"
    - ".md"
    - ".txt"
    - ".json"
    - ".yaml"
    - ".yml"
    - ".go"
    - ".java"
    - ".cpp"
    - ".c"
    - ".h"
  temp_directory: "/app/temp"

security:
  jwt_expiration: 1800  # 30 minutes
  rate_limiting:
    enabled: true
    requests: 1000
    window: 3600

monitoring:
  metrics:
    enabled: true
    port: 9090
  health_check:
    enabled: true
    endpoint: "/health"
  sentry:
    enabled: true
    sample_rate: 0.1
