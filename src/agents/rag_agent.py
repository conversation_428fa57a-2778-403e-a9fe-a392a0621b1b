"""
RAG Retrieval Agent

This module provides a direct retrieval agent for factual and code lookup queries
without complex reasoning or generation.
"""

import logging
import time
from typing import Any

from ..config import Settings
from ..ingestion.integration_example import IntegratedIngestionPipeline
from .base import Agent, AgentContext, AgentResponse, AgentType
from .exceptions import Agent<PERSON>rror
from .formatters import ResponseFormatter

logger = logging.getLogger(__name__)


class RAGRetrievalAgent(Agent):
    """Agent for direct retrieval and factual lookups."""

    def __init__(
        self,
        settings: Settings,
        formatter: ResponseFormatter,
        ingestion_pipeline: IntegratedIngestionPipeline,
    ):
        super().__init__(AgentType.RAG_RETRIEVAL, settings)
        self.formatter = formatter
        self.ingestion_pipeline = ingestion_pipeline

        # Configuration
        self.max_results = 10
        self.min_similarity_threshold = 0.7

        logger.info("Initialized RAG Retrieval Agent")

    def can_handle_query(self, query: str, context: AgentContext) -> float:
        """Return confidence score for handling this query."""
        # RAG agent handles factual, code lookup, and simple queries
        factual_keywords = [
            "what is",
            "how does",
            "where is",
            "find",
            "show me",
            "explain",
            "define",
            "list",
            "search",
            "lookup",
        ]

        code_keywords = [
            "function",
            "class",
            "method",
            "variable",
            "import",
            "module",
            "file",
            "code",
            "implementation",
        ]

        query_lower = query.lower()

        # High confidence for direct factual queries
        if any(keyword in query_lower for keyword in factual_keywords):
            return 0.9

        # High confidence for code lookup queries
        if any(keyword in query_lower for keyword in code_keywords):
            return 0.8

        # Medium confidence for simple questions
        if len(query.split()) <= 10 and "?" in query:
            return 0.6

        # Low confidence for complex queries (let other agents handle)
        if any(keyword in query_lower for keyword in ["design", "architecture", "plan", "implement", "create"]):
            return 0.2

        # Default medium-low confidence
        return 0.4

    async def process_query(self, query: str, context: AgentContext) -> AgentResponse:
        """Process a query using direct retrieval."""
        start_time = time.time()

        try:
            logger.info(f"Processing RAG retrieval query: {query[:100]}...")

            # Perform semantic search
            search_results = await self._perform_search(query, context)

            # Generate response based on search results
            response_content = self._generate_response_content(query, search_results)

            # Extract sources
            sources = self._extract_sources(search_results)

            processing_time = time.time() - start_time

            # Create response
            response = AgentResponse(
                agent_type=self.agent_type,
                content=response_content,
                confidence=self._calculate_confidence(search_results),
                sources=sources,
                processing_time=processing_time,
                metadata={
                    "search_results_count": len(search_results),
                    "query_type": "retrieval",
                    "method": "semantic_search",
                },
            )

            self._update_stats(processing_time, success=True)
            logger.info(f"RAG retrieval completed in {processing_time:.2f}s")

            return response

        except Exception as e:
            processing_time = time.time() - start_time
            self._update_stats(processing_time, success=False)

            logger.error(f"RAG retrieval failed: {e}")
            raise AgentError(
                f"RAG retrieval failed: {e}",
                agent_type=self.agent_type.value,
                details={"query": query[:100]},
                cause=e,
            ) from e

    async def _perform_search(self, query: str, context: AgentContext) -> list[Any]:
        """Perform semantic search using the ingestion pipeline."""
        try:
            # Use existing search results if available
            if context.search_results:
                logger.debug("Using existing search results from context")
                return context.search_results[: self.max_results]

            # Perform new search
            filters = {}

            # Add repository filter if available
            if context.repository_context and "repository_url" in context.repository_context:
                filters["repository"] = context.repository_context["repository_url"]

            # Perform search
            search_results = await self.ingestion_pipeline.search_repository(
                query=query, top_k=self.max_results, filters=filters
            )

            # Convert to SearchResult objects if needed
            if search_results and isinstance(search_results[0], dict):
                # Convert dictionary results to SearchResult-like objects
                converted_results = []
                for result in search_results:
                    # Create a simple object with the expected attributes
                    class SimpleSearchResult:
                        def __init__(self, data):
                            self.content = data.get("content", "")
                            self.similarity_score = data.get("similarity_score", 0.0)
                            self.file_path = data.get("file_path", "")
                            self.chunk_type = data.get("chunk_type", "")
                            self.line_range = data.get("line_range", None)

                    converted_results.append(SimpleSearchResult(result))

                return converted_results

            return search_results or []

        except Exception as e:
            logger.error(f"Search failed: {e}")
            return []

    def _generate_response_content(self, query: str, search_results: list[Any]) -> str:
        """Generate response content based on search results."""
        if not search_results:
            return (
                f"I couldn't find any relevant information for: {query}\n\n"
                f"Please try rephrasing your query or check if the repository "
                f"has been properly indexed."
            )

        # Filter results by similarity threshold
        relevant_results = [
            result
            for result in search_results
            if getattr(result, "similarity_score", 0) >= self.min_similarity_threshold
        ]

        if not relevant_results:
            return (
                f"I found some results for '{query}', but they don't seem "
                f"highly relevant. You might want to try a more specific query."
            )

        # Generate response based on query type
        if self._is_code_query(query):
            return self._generate_code_response(query, relevant_results)
        return self._generate_factual_response(query, relevant_results)

    def _is_code_query(self, query: str) -> bool:
        """Check if the query is asking for code."""
        code_indicators = [
            "function",
            "class",
            "method",
            "implementation",
            "code",
            "how to implement",
            "show code",
            "example",
        ]
        return any(indicator in query.lower() for indicator in code_indicators)

    def _generate_code_response(self, query: str, results: list[Any]) -> str:
        """Generate a code-focused response."""
        response_parts = [f"Here's what I found for: **{query}**\n"]

        for i, result in enumerate(results[:5], 1):
            file_path = getattr(result, "file_path", "Unknown file")
            content = getattr(result, "content", "")
            score = getattr(result, "similarity_score", 0)

            response_parts.append(f"## {i}. {file_path} (Relevance: {score:.1%})")

            # Format code content
            if content:
                # Detect language from file extension
                language = self._detect_language(file_path)
                response_parts.append(f"```{language}\n{content}\n```")

            response_parts.append("")  # Empty line for spacing

        return "\n".join(response_parts)

    def _generate_factual_response(self, query: str, results: list[Any]) -> str:
        """Generate a factual response."""
        response_parts = [f"Based on the repository content, here's what I found for: **{query}**\n"]

        for i, result in enumerate(results[:3], 1):
            file_path = getattr(result, "file_path", "Unknown file")
            content = getattr(result, "content", "")
            score = getattr(result, "similarity_score", 0)

            response_parts.append(f"### {i}. From {file_path}")

            if content:
                # Truncate long content
                if len(content) > 500:
                    content = content[:500] + "..."
                response_parts.append(content)

            response_parts.append(f"*Relevance: {score:.1%}*\n")

        return "\n".join(response_parts)

    def _detect_language(self, file_path: str) -> str:
        """Detect programming language from file extension."""
        extension_map = {
            ".py": "python",
            ".js": "javascript",
            ".ts": "typescript",
            ".java": "java",
            ".cpp": "cpp",
            ".c": "c",
            ".go": "go",
            ".rs": "rust",
            ".php": "php",
            ".rb": "ruby",
            ".sh": "bash",
            ".sql": "sql",
            ".json": "json",
            ".yaml": "yaml",
            ".yml": "yaml",
            ".xml": "xml",
            ".html": "html",
            ".css": "css",
            ".md": "markdown",
        }

        for ext, lang in extension_map.items():
            if file_path.lower().endswith(ext):
                return lang

        return "text"

    def _extract_sources(self, search_results: list[Any]) -> list[str]:
        """Extract source citations from search results."""
        sources = []
        for result in search_results:
            file_path = getattr(result, "file_path", "")
            line_range = getattr(result, "line_range", None)

            if file_path:
                if line_range:
                    sources.append(f"{file_path}:{line_range[0]}-{line_range[1]}")
                else:
                    sources.append(file_path)

        return sources

    def _calculate_confidence(self, search_results: list[Any]) -> float:
        """Calculate confidence based on search results quality."""
        if not search_results:
            return 0.1

        # Calculate average similarity score
        scores = [getattr(result, "similarity_score", 0) for result in search_results]
        avg_score = sum(scores) / len(scores) if scores else 0

        # Adjust confidence based on number of results and average score
        result_count_factor = min(len(search_results) / 5, 1.0)  # Max factor at 5+ results

        return min(avg_score * result_count_factor, 0.95)  # Cap at 95%
