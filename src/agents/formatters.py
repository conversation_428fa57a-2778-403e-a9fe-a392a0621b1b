"""
Response Formatters

This module provides utilities for formatting agent responses with consistent
Markdown formatting, source citations, and structured output.
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
import logging
import re
from typing import Any

from ..ingestion.base import SearchResult
from .base import AgentResponse

logger = logging.getLogger(__name__)


@dataclass
class FormattedResponse:
    """A formatted response with metadata."""

    content: str
    format_type: str
    sources: list[str]
    metadata: dict[str, Any]


class ResponseFormatter(ABC):
    """Abstract base class for response formatters."""

    @abstractmethod
    def format_response(
        self, response: AgentResponse, search_results: list[SearchResult] | None = None
    ) -> FormattedResponse:
        """Format an agent response."""
        pass

    @abstractmethod
    def format_error(self, error_message: str, error_type: str = "error") -> FormattedResponse:
        """Format an error message."""
        pass


class MarkdownFormatter(ResponseFormatter):
    """Formats responses in Markdown with proper structure and citations."""

    def __init__(self):
        self.format_type = "markdown"

    def format_response(
        self, response: AgentResponse, search_results: list[SearchResult] | None = None
    ) -> FormattedResponse:
        """Format agent response as Markdown."""
        try:
            # Start with the main content
            formatted_content = self._format_main_content(response.content)

            # Add source citations
            if response.sources or search_results:
                citations = self._format_citations(response.sources, search_results)
                if citations:
                    formatted_content += "\n\n" + citations

            # Add metadata if present
            if response.metadata:
                metadata_section = self._format_metadata(response.metadata)
                if metadata_section:
                    formatted_content += "\n\n" + metadata_section

            return FormattedResponse(
                content=formatted_content,
                format_type=self.format_type,
                sources=response.sources,
                metadata={
                    "agent_type": response.agent_type.value,
                    "confidence": response.confidence,
                    "processing_time": response.processing_time,
                    "timestamp": response.timestamp.isoformat(),
                },
            )

        except Exception as e:
            logger.error(f"Error formatting response: {e}")
            return self.format_error(f"Error formatting response: {e}")

    def format_error(self, error_message: str, error_type: str = "error") -> FormattedResponse:
        """Format an error message as Markdown."""
        content = f"## ⚠️ {error_type.title()}\n\n{error_message}"

        return FormattedResponse(
            content=content,
            format_type=self.format_type,
            sources=[],
            metadata={"error_type": error_type},
        )

    def _format_main_content(self, content: str) -> str:
        """Format the main content with proper Markdown structure."""
        # Ensure proper heading hierarchy
        content = self._normalize_headings(content)

        # Format code blocks
        content = self._format_code_blocks(content)

        # Format lists
        content = self._format_lists(content)

        return content.strip()

    def _normalize_headings(self, content: str) -> str:
        """Normalize heading levels to ensure proper hierarchy."""
        lines = content.split("\n")
        normalized_lines = []

        for line in lines:
            # Check if line is a heading
            if line.strip().startswith("#"):
                # Ensure there's a space after #
                heading_match = re.match(r"^(#+)\s*(.*)", line.strip())
                if heading_match:
                    level = len(heading_match.group(1))
                    text = heading_match.group(2)
                    # Limit heading levels to 6
                    level = min(level, 6)
                    normalized_lines.append("#" * level + " " + text)
                else:
                    normalized_lines.append(line)
            else:
                normalized_lines.append(line)

        return "\n".join(normalized_lines)

    def _format_code_blocks(self, content: str) -> str:
        """Ensure code blocks are properly formatted."""
        # Add language hints to code blocks if missing
        content = re.sub(r"```\n((?:(?!```).)*)\n```", r"```\n\1\n```", content, flags=re.DOTALL)

        return content

    def _format_lists(self, content: str) -> str:
        """Ensure lists are properly formatted."""
        lines = content.split("\n")
        formatted_lines = []

        for line in lines:
            stripped = line.strip()
            # Fix bullet points
            if stripped.startswith("*") and not stripped.startswith("* "):
                formatted_lines.append(line.replace("*", "* ", 1))
            elif stripped.startswith("-") and not stripped.startswith("- "):
                formatted_lines.append(line.replace("-", "- ", 1))
            else:
                formatted_lines.append(line)

        return "\n".join(formatted_lines)

    def _format_citations(self, sources: list[str], search_results: list[SearchResult] | None = None) -> str:
        """Format source citations."""
        all_sources = set(sources)

        # Add sources from search results
        if search_results:
            for result in search_results:
                file_path = result.chunk.file_metadata.file_path
                line_range = result.chunk.line_range
                if line_range:
                    source = f"{file_path}:{line_range[0]}-{line_range[1]}"
                else:
                    source = file_path
                all_sources.add(source)

        if not all_sources:
            return ""

        citation_lines = ["## 📚 Sources"]
        for i, source in enumerate(sorted(all_sources), 1):
            citation_lines.append(f"{i}. `{source}`")

        return "\n".join(citation_lines)

    def _format_metadata(self, metadata: dict[str, Any]) -> str:
        """Format metadata section."""
        if not metadata:
            return ""

        # Only include relevant metadata for display
        display_metadata = {}
        for key, value in metadata.items():
            if key in ["confidence", "processing_time", "model_used"]:
                display_metadata[key] = value

        if not display_metadata:
            return ""

        lines = ["## ℹ️ Response Metadata"]
        for key, value in display_metadata.items():
            formatted_key = key.replace("_", " ").title()
            if key == "confidence":
                lines.append(f"- **{formatted_key}**: {value:.2%}")
            elif key == "processing_time":
                lines.append(f"- **{formatted_key}**: {value:.2f}s")
            else:
                lines.append(f"- **{formatted_key}**: {value}")

        return "\n".join(lines)


class SourceCitationFormatter:
    """Specialized formatter for source citations."""

    @staticmethod
    def format_inline_citation(file_path: str, line_range: tuple | None = None) -> str:
        """Format an inline citation."""
        if line_range:
            return f"[{file_path}:{line_range[0]}-{line_range[1]}]"
        return f"[{file_path}]"

    @staticmethod
    def format_reference_list(search_results: list[SearchResult]) -> str:
        """Format a reference list from search results."""
        if not search_results:
            return ""

        lines = ["## References"]
        for i, result in enumerate(search_results, 1):
            file_path = result.chunk.file_metadata.file_path
            line_range = result.chunk.line_range
            score = result.similarity_score

            if line_range:
                citation = f"{file_path}:{line_range[0]}-{line_range[1]}"
            else:
                citation = file_path

            lines.append(f"{i}. `{citation}` (relevance: {score:.1%})")

        return "\n".join(lines)

    @staticmethod
    def extract_citations_from_content(content: str) -> list[str]:
        """Extract citation patterns from content."""
        # Pattern to match [filename:line-range] or [filename]
        citation_pattern = r"\[([^\]]+(?::\d+-\d+)?)\]"
        matches = re.findall(citation_pattern, content)
        return matches

    @staticmethod
    def add_citations_to_content(content: str, search_results: list[SearchResult], max_citations: int = 5) -> str:
        """Add relevant citations to content based on search results."""
        if not search_results:
            return content

        # Get top results for citation
        top_results = search_results[:max_citations]

        # Add citations at the end of paragraphs that might benefit
        paragraphs = content.split("\n\n")
        enhanced_paragraphs = []

        for paragraph in paragraphs:
            enhanced_paragraph = paragraph

            # Check if paragraph discusses code or implementation
            if any(
                keyword in paragraph.lower()
                for keyword in [
                    "implement",
                    "function",
                    "class",
                    "method",
                    "code",
                    "example",
                ]
            ):

                # Find relevant citation
                for result in top_results:
                    file_path = result.chunk.file_metadata.file_path
                    if any(term in file_path.lower() for term in ["example", "impl", "src", "lib"]):
                        citation = SourceCitationFormatter.format_inline_citation(file_path, result.chunk.line_range)
                        enhanced_paragraph += f" {citation}"
                        break

            enhanced_paragraphs.append(enhanced_paragraph)

        return "\n\n".join(enhanced_paragraphs)
