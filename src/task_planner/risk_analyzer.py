"""
Risk Assessment and Mitigation

This module assesses implementation risks, identifies potential issues,
and suggests mitigation strategies for task plans.
"""

import logging
import re
from typing import Any

from .models import (
    DependencyGraph,
    RiskLevel,
    Task,
    TaskPlan,
    TaskRisk,
    TaskType,
    Timeline,
)

logger = logging.getLogger(__name__)


class RiskAnalyzer:
    """Analyzes risks in task plans and suggests mitigation strategies."""

    def __init__(self):
        """Initialize the risk analyzer."""
        # Define risk indicators based on task characteristics
        self.risk_indicators = {
            "complexity": {
                "high_effort": {"threshold": 80, "level": RiskLevel.HIGH},  # > 2 weeks
                "medium_effort": {"threshold": 40, "level": RiskLevel.MEDIUM},  # > 1 week
            },
            "dependencies": {
                "high_dependency": {"threshold": 5, "level": RiskLevel.HIGH},
                "medium_dependency": {"threshold": 3, "level": RiskLevel.MEDIUM},
            },
            "technology": {
                "new_technology": ["new", "experimental", "beta", "alpha"],
                "integration": ["integrate", "connect", "interface", "api"],
                "migration": ["migrate", "upgrade", "convert", "transform"],
            },
            "external": {
                "third_party": ["third party", "external", "vendor", "library"],
                "api_dependency": ["api", "service", "endpoint", "rest"],
                "data_migration": ["data", "migration", "import", "export"],
            },
        }

        # Define mitigation strategies for different risk types
        self.mitigation_strategies = {
            "high_complexity": [
                "Break down into smaller, manageable tasks",
                "Conduct proof of concept before full implementation",
                "Add additional buffer time for unexpected issues",
                "Assign experienced team members",
                "Plan for regular progress reviews",
            ],
            "dependency_risk": [
                "Identify alternative approaches if dependencies fail",
                "Create mock implementations for testing",
                "Establish clear communication with dependency owners",
                "Plan for parallel development where possible",
            ],
            "technology_risk": [
                "Conduct technology evaluation and prototyping",
                "Plan for learning curve and training time",
                "Have fallback technology options ready",
                "Engage with technology community for support",
            ],
            "external_risk": [
                "Establish SLAs and communication protocols",
                "Plan for vendor/service unavailability",
                "Create backup plans and alternative providers",
                "Test integration points early and frequently",
            ],
            "timeline_risk": [
                "Add buffer time to critical path tasks",
                "Identify tasks that can be parallelized",
                "Plan for resource reallocation if needed",
                "Establish clear milestone checkpoints",
            ],
            "resource_risk": [
                "Cross-train team members on critical skills",
                "Plan for knowledge transfer sessions",
                "Document key decisions and approaches",
                "Identify backup resources if needed",
            ],
        }

        logger.info("Initialized risk analyzer")

    def analyze_task_plan_risks(self, task_plan: TaskPlan) -> dict[str, Any]:
        """Analyze risks in a complete task plan.
        
        Args:
            task_plan: Task plan to analyze
            
        Returns:
            Risk analysis report
        """
        logger.info(f"Analyzing risks for task plan: {task_plan.title}")

        risk_analysis = {
            "overall_risk_level": RiskLevel.LOW,
            "risk_score": 0.0,  # 0.0 to 1.0
            "task_risks": [],
            "timeline_risks": [],
            "dependency_risks": [],
            "resource_risks": [],
            "mitigation_plan": [],
            "recommendations": [],
        }

        # Analyze individual task risks
        for task in task_plan.tasks:
            task_risk_analysis = self.analyze_task_risks(task)
            risk_analysis["task_risks"].append(task_risk_analysis)

        # Analyze timeline risks
        timeline_risks = self.analyze_timeline_risks(task_plan.timeline)
        risk_analysis["timeline_risks"] = timeline_risks

        # Analyze dependency risks
        dependency_risks = self.analyze_dependency_risks(task_plan.dependency_graph)
        risk_analysis["dependency_risks"] = dependency_risks

        # Analyze resource risks
        resource_risks = self.analyze_resource_risks(task_plan.tasks)
        risk_analysis["resource_risks"] = resource_risks

        # Calculate overall risk level and score
        risk_analysis["overall_risk_level"], risk_analysis["risk_score"] = self._calculate_overall_risk(
            risk_analysis
        )

        # Generate mitigation plan
        risk_analysis["mitigation_plan"] = self._generate_mitigation_plan(risk_analysis)

        # Generate recommendations
        risk_analysis["recommendations"] = self._generate_recommendations(risk_analysis)

        logger.info(f"Risk analysis complete: {risk_analysis['overall_risk_level'].value} risk, score: {risk_analysis['risk_score']:.2f}")
        return risk_analysis

    def analyze_task_risks(self, task: Task) -> dict[str, Any]:
        """Analyze risks for a single task.
        
        Args:
            task: Task to analyze
            
        Returns:
            Task risk analysis
        """
        task_analysis = {
            "task_id": task.id,
            "task_title": task.title,
            "identified_risks": [],
            "risk_level": RiskLevel.LOW,
            "risk_score": 0.0,
        }

        # Analyze effort-based risks
        effort_risks = self._analyze_effort_risks(task)
        task_analysis["identified_risks"].extend(effort_risks)

        # Analyze dependency-based risks
        dependency_risks = self._analyze_task_dependency_risks(task)
        task_analysis["identified_risks"].extend(dependency_risks)

        # Analyze technology-based risks
        technology_risks = self._analyze_technology_risks(task)
        task_analysis["identified_risks"].extend(technology_risks)

        # Analyze external dependency risks
        external_risks = self._analyze_external_risks(task)
        task_analysis["identified_risks"].extend(external_risks)

        # Calculate task risk level and score
        if task_analysis["identified_risks"]:
            risk_levels = [risk["level"] for risk in task_analysis["identified_risks"]]
            task_analysis["risk_level"] = max(risk_levels, key=lambda x: x.value)
            task_analysis["risk_score"] = sum(risk["probability"] * risk["impact_score"] for risk in task_analysis["identified_risks"]) / len(task_analysis["identified_risks"])

        return task_analysis

    def analyze_timeline_risks(self, timeline: Timeline) -> list[dict[str, Any]]:
        """Analyze timeline-related risks.
        
        Args:
            timeline: Timeline to analyze
            
        Returns:
            List of timeline risks
        """
        timeline_risks = []

        # Check for aggressive timeline
        total_days = (timeline.end_date - timeline.start_date).days
        total_hours = timeline.total_estimated_hours
        implied_hours_per_day = total_hours / max(total_days, 1)

        if implied_hours_per_day > 10:  # More than 10 hours per day
            timeline_risks.append({
                "type": "aggressive_timeline",
                "description": f"Timeline requires {implied_hours_per_day:.1f} hours per day",
                "level": RiskLevel.HIGH,
                "probability": 0.8,
                "impact": "High likelihood of delays and quality issues",
                "impact_score": 0.9,
                "mitigation": "Extend timeline or add resources"
            })
        elif implied_hours_per_day > 8:  # More than 8 hours per day
            timeline_risks.append({
                "type": "tight_timeline",
                "description": f"Timeline requires {implied_hours_per_day:.1f} hours per day",
                "level": RiskLevel.MEDIUM,
                "probability": 0.6,
                "impact": "Potential for delays if issues arise",
                "impact_score": 0.6,
                "mitigation": "Add buffer time or monitor progress closely"
            })

        # Check for critical path risks
        if timeline.critical_path and len(timeline.critical_path) > len(timeline.tasks) * 0.7:
            timeline_risks.append({
                "type": "long_critical_path",
                "description": f"Critical path contains {len(timeline.critical_path)} of {len(timeline.tasks)} tasks",
                "level": RiskLevel.MEDIUM,
                "probability": 0.5,
                "impact": "Limited flexibility for delays",
                "impact_score": 0.7,
                "mitigation": "Look for parallelization opportunities"
            })

        # Check for milestone risks
        if len(timeline.milestones) < 3 and total_days > 30:  # Long project with few milestones
            timeline_risks.append({
                "type": "insufficient_milestones",
                "description": f"Only {len(timeline.milestones)} milestones for {total_days}-day project",
                "level": RiskLevel.MEDIUM,
                "probability": 0.4,
                "impact": "Difficult to track progress and identify issues early",
                "impact_score": 0.5,
                "mitigation": "Add intermediate milestones and checkpoints"
            })

        return timeline_risks

    def analyze_dependency_risks(self, dependency_graph: DependencyGraph) -> list[dict[str, Any]]:
        """Analyze dependency-related risks.
        
        Args:
            dependency_graph: Dependency graph to analyze
            
        Returns:
            List of dependency risks
        """
        dependency_risks = []

        # Check for circular dependencies
        if dependency_graph.has_cycle():
            dependency_risks.append({
                "type": "circular_dependencies",
                "description": "Dependency graph contains cycles",
                "level": RiskLevel.HIGH,
                "probability": 1.0,
                "impact": "Tasks cannot be scheduled properly",
                "impact_score": 1.0,
                "mitigation": "Resolve circular dependencies by restructuring tasks"
            })

        # Check for high fan-in/fan-out
        for task_id, task in dependency_graph.nodes.items():
            predecessors = dependency_graph.get_predecessors(task_id)
            successors = dependency_graph.get_successors(task_id)

            if len(predecessors) > 5:
                dependency_risks.append({
                    "type": "high_fan_in",
                    "task_id": task_id,
                    "description": f"Task {task.title} has {len(predecessors)} dependencies",
                    "level": RiskLevel.MEDIUM,
                    "probability": 0.6,
                    "impact": "High coordination overhead and delay risk",
                    "impact_score": 0.7,
                    "mitigation": "Simplify dependencies or add coordination buffer"
                })

            if len(successors) > 6:
                dependency_risks.append({
                    "type": "high_fan_out",
                    "task_id": task_id,
                    "description": f"Task {task.title} blocks {len(successors)} other tasks",
                    "level": RiskLevel.HIGH,
                    "probability": 0.7,
                    "impact": "Single point of failure for multiple tasks",
                    "impact_score": 0.8,
                    "mitigation": "Prioritize this task and add buffer time"
                })

        return dependency_risks

    def analyze_resource_risks(self, tasks: list[Task]) -> list[dict[str, Any]]:
        """Analyze resource-related risks.
        
        Args:
            tasks: List of tasks to analyze
            
        Returns:
            List of resource risks
        """
        resource_risks = []

        # Analyze skill requirements
        skill_requirements = {}
        for task in tasks:
            # Infer skills from task type and description
            skills = self._infer_required_skills(task)
            for skill in skills:
                if skill not in skill_requirements:
                    skill_requirements[skill] = []
                skill_requirements[skill].append(task.id)

        # Check for skill bottlenecks
        for skill, task_ids in skill_requirements.items():
            if len(task_ids) > 5:  # Many tasks requiring same skill
                total_hours = sum(task.estimate_hours for task in tasks if task.id in task_ids)
                resource_risks.append({
                    "type": "skill_bottleneck",
                    "skill": skill,
                    "description": f"{len(task_ids)} tasks require {skill} skill ({total_hours:.1f} hours)",
                    "level": RiskLevel.MEDIUM,
                    "probability": 0.5,
                    "impact": "Resource contention and potential delays",
                    "impact_score": 0.6,
                    "mitigation": "Cross-train team members or add specialized resources"
                })

        # Check for knowledge concentration
        specialized_tasks = [task for task in tasks if task.estimate_hours > 40]  # Large tasks
        if len(specialized_tasks) > 0:
            resource_risks.append({
                "type": "knowledge_concentration",
                "description": f"{len(specialized_tasks)} large tasks may require specialized knowledge",
                "level": RiskLevel.MEDIUM,
                "probability": 0.4,
                "impact": "Risk if key team members are unavailable",
                "impact_score": 0.7,
                "mitigation": "Document approaches and cross-train team members"
            })

        return resource_risks

    def _analyze_effort_risks(self, task: Task) -> list[dict[str, Any]]:
        """Analyze effort-based risks for a task."""
        risks = []

        if task.estimate_hours > self.risk_indicators["complexity"]["high_effort"]["threshold"]:
            risks.append({
                "type": "high_complexity",
                "description": f"Task requires {task.estimate_hours} hours (>2 weeks)",
                "level": RiskLevel.HIGH,
                "probability": 0.7,
                "impact": "High risk of underestimation and delays",
                "impact_score": 0.8,
            })
        elif task.estimate_hours > self.risk_indicators["complexity"]["medium_effort"]["threshold"]:
            risks.append({
                "type": "medium_complexity",
                "description": f"Task requires {task.estimate_hours} hours (>1 week)",
                "level": RiskLevel.MEDIUM,
                "probability": 0.5,
                "impact": "Moderate risk of estimation errors",
                "impact_score": 0.6,
            })

        return risks

    def _analyze_task_dependency_risks(self, task: Task) -> list[dict[str, Any]]:
        """Analyze dependency-based risks for a task."""
        risks = []

        dependency_count = len(task.dependencies)
        if dependency_count > self.risk_indicators["dependencies"]["high_dependency"]["threshold"]:
            risks.append({
                "type": "high_dependencies",
                "description": f"Task has {dependency_count} dependencies",
                "level": RiskLevel.HIGH,
                "probability": 0.6,
                "impact": "High coordination overhead and delay risk",
                "impact_score": 0.7,
            })
        elif dependency_count > self.risk_indicators["dependencies"]["medium_dependency"]["threshold"]:
            risks.append({
                "type": "medium_dependencies",
                "description": f"Task has {dependency_count} dependencies",
                "level": RiskLevel.MEDIUM,
                "probability": 0.4,
                "impact": "Moderate coordination complexity",
                "impact_score": 0.5,
            })

        return risks

    def _analyze_technology_risks(self, task: Task) -> list[dict[str, Any]]:
        """Analyze technology-based risks for a task."""
        risks = []
        task_text = f"{task.title} {task.description}".lower()

        # Check for new technology indicators
        new_tech_indicators = self.risk_indicators["technology"]["new_technology"]
        if any(indicator in task_text for indicator in new_tech_indicators):
            risks.append({
                "type": "new_technology",
                "description": "Task involves new or experimental technology",
                "level": RiskLevel.MEDIUM,
                "probability": 0.6,
                "impact": "Learning curve and potential compatibility issues",
                "impact_score": 0.6,
            })

        # Check for integration indicators
        integration_indicators = self.risk_indicators["technology"]["integration"]
        if any(indicator in task_text for indicator in integration_indicators):
            risks.append({
                "type": "integration_complexity",
                "description": "Task involves system integration",
                "level": RiskLevel.MEDIUM,
                "probability": 0.5,
                "impact": "Potential compatibility and interface issues",
                "impact_score": 0.6,
            })

        # Check for migration indicators
        migration_indicators = self.risk_indicators["technology"]["migration"]
        if any(indicator in task_text for indicator in migration_indicators):
            risks.append({
                "type": "migration_risk",
                "description": "Task involves data or system migration",
                "level": RiskLevel.HIGH,
                "probability": 0.7,
                "impact": "Data loss or corruption risk",
                "impact_score": 0.8,
            })

        return risks

    def _analyze_external_risks(self, task: Task) -> list[dict[str, Any]]:
        """Analyze external dependency risks for a task."""
        risks = []
        task_text = f"{task.title} {task.description}".lower()

        # Check for third-party dependencies
        third_party_indicators = self.risk_indicators["external"]["third_party"]
        if any(indicator in task_text for indicator in third_party_indicators):
            risks.append({
                "type": "third_party_dependency",
                "description": "Task depends on third-party components",
                "level": RiskLevel.MEDIUM,
                "probability": 0.4,
                "impact": "External factors beyond team control",
                "impact_score": 0.6,
            })

        # Check for API dependencies
        api_indicators = self.risk_indicators["external"]["api_dependency"]
        if any(indicator in task_text for indicator in api_indicators):
            risks.append({
                "type": "api_dependency",
                "description": "Task depends on external APIs or services",
                "level": RiskLevel.MEDIUM,
                "probability": 0.5,
                "impact": "Service availability and rate limiting risks",
                "impact_score": 0.5,
            })

        return risks

    def _calculate_overall_risk(self, risk_analysis: dict[str, Any]) -> tuple[RiskLevel, float]:
        """Calculate overall risk level and score."""
        all_risks = []
        
        # Collect all risks
        for task_risk in risk_analysis["task_risks"]:
            all_risks.extend(task_risk["identified_risks"])
        
        all_risks.extend(risk_analysis["timeline_risks"])
        all_risks.extend(risk_analysis["dependency_risks"])
        all_risks.extend(risk_analysis["resource_risks"])

        if not all_risks:
            return RiskLevel.LOW, 0.0

        # Calculate weighted risk score
        total_score = 0.0
        total_weight = 0.0

        for risk in all_risks:
            probability = risk.get("probability", 0.5)
            impact_score = risk.get("impact_score", 0.5)
            risk_score = probability * impact_score
            
            # Weight by risk level
            level_weights = {
                RiskLevel.LOW: 1.0,
                RiskLevel.MEDIUM: 2.0,
                RiskLevel.HIGH: 3.0,
                RiskLevel.CRITICAL: 4.0,
            }
            weight = level_weights.get(risk["level"], 2.0)
            
            total_score += risk_score * weight
            total_weight += weight

        overall_score = total_score / total_weight if total_weight > 0 else 0.0

        # Determine overall risk level
        if overall_score > 0.7:
            overall_level = RiskLevel.HIGH
        elif overall_score > 0.4:
            overall_level = RiskLevel.MEDIUM
        else:
            overall_level = RiskLevel.LOW

        return overall_level, overall_score

    def _generate_mitigation_plan(self, risk_analysis: dict[str, Any]) -> list[dict[str, Any]]:
        """Generate mitigation plan based on identified risks."""
        mitigation_plan = []

        # Collect risk types
        risk_types = set()
        for task_risk in risk_analysis["task_risks"]:
            for risk in task_risk["identified_risks"]:
                risk_types.add(risk["type"])
        
        for risk in risk_analysis["timeline_risks"]:
            risk_types.add(risk["type"])
        
        for risk in risk_analysis["dependency_risks"]:
            risk_types.add(risk["type"])
        
        for risk in risk_analysis["resource_risks"]:
            risk_types.add(risk["type"])

        # Generate mitigation strategies
        for risk_type in risk_types:
            # Map risk types to mitigation categories
            mitigation_category = self._map_risk_to_mitigation_category(risk_type)
            strategies = self.mitigation_strategies.get(mitigation_category, [])
            
            if strategies:
                mitigation_plan.append({
                    "risk_type": risk_type,
                    "category": mitigation_category,
                    "strategies": strategies,
                    "priority": self._get_mitigation_priority(risk_type, risk_analysis),
                })

        return mitigation_plan

    def _generate_recommendations(self, risk_analysis: dict[str, Any]) -> list[str]:
        """Generate high-level recommendations based on risk analysis."""
        recommendations = []

        overall_risk = risk_analysis["overall_risk_level"]
        risk_score = risk_analysis["risk_score"]

        if overall_risk == RiskLevel.HIGH:
            recommendations.append("Consider breaking down high-risk tasks into smaller components")
            recommendations.append("Add significant buffer time to the timeline")
            recommendations.append("Assign experienced team members to critical tasks")
            recommendations.append("Implement frequent progress checkpoints and reviews")

        if risk_score > 0.5:
            recommendations.append("Develop contingency plans for high-probability risks")
            recommendations.append("Consider parallel development approaches where possible")

        # Specific recommendations based on risk types
        has_dependency_risks = any(risk_analysis["dependency_risks"])
        if has_dependency_risks:
            recommendations.append("Review and simplify task dependencies where possible")

        has_timeline_risks = any(risk_analysis["timeline_risks"])
        if has_timeline_risks:
            recommendations.append("Re-evaluate timeline assumptions and add buffer time")

        has_resource_risks = any(risk_analysis["resource_risks"])
        if has_resource_risks:
            recommendations.append("Plan for knowledge transfer and cross-training")

        return recommendations

    def _infer_required_skills(self, task: Task) -> list[str]:
        """Infer required skills from task characteristics."""
        skills = []
        task_text = f"{task.title} {task.description}".lower()

        # Programming languages
        if any(lang in task_text for lang in ["python", "javascript", "java", "c++", "go"]):
            skills.append("programming")

        # Specific technologies
        if any(tech in task_text for tech in ["database", "sql", "mongodb"]):
            skills.append("database")
        
        if any(tech in task_text for tech in ["api", "rest", "graphql"]):
            skills.append("api_development")
        
        if any(tech in task_text for tech in ["frontend", "ui", "react", "vue"]):
            skills.append("frontend")
        
        if any(tech in task_text for tech in ["backend", "server", "microservice"]):
            skills.append("backend")
        
        if any(tech in task_text for tech in ["test", "testing", "qa"]):
            skills.append("testing")
        
        if any(tech in task_text for tech in ["deploy", "devops", "ci/cd"]):
            skills.append("devops")

        # Task type based skills
        if task.task_type == TaskType.DESIGN:
            skills.append("system_design")
        elif task.task_type == TaskType.ANALYSIS:
            skills.append("analysis")
        elif task.task_type == TaskType.DOCUMENTATION:
            skills.append("documentation")

        return skills if skills else ["general"]

    def _map_risk_to_mitigation_category(self, risk_type: str) -> str:
        """Map risk type to mitigation category."""
        mapping = {
            "high_complexity": "high_complexity",
            "medium_complexity": "high_complexity",
            "high_dependencies": "dependency_risk",
            "medium_dependencies": "dependency_risk",
            "circular_dependencies": "dependency_risk",
            "high_fan_in": "dependency_risk",
            "high_fan_out": "dependency_risk",
            "new_technology": "technology_risk",
            "integration_complexity": "technology_risk",
            "migration_risk": "technology_risk",
            "third_party_dependency": "external_risk",
            "api_dependency": "external_risk",
            "aggressive_timeline": "timeline_risk",
            "tight_timeline": "timeline_risk",
            "long_critical_path": "timeline_risk",
            "skill_bottleneck": "resource_risk",
            "knowledge_concentration": "resource_risk",
        }
        return mapping.get(risk_type, "high_complexity")

    def _get_mitigation_priority(self, risk_type: str, risk_analysis: dict[str, Any]) -> str:
        """Get priority for mitigation based on risk characteristics."""
        # Count occurrences of this risk type
        count = 0
        max_level = RiskLevel.LOW

        for task_risk in risk_analysis["task_risks"]:
            for risk in task_risk["identified_risks"]:
                if risk["type"] == risk_type:
                    count += 1
                    if risk["level"].value > max_level.value:
                        max_level = risk["level"]

        for risk_list in [risk_analysis["timeline_risks"], risk_analysis["dependency_risks"], risk_analysis["resource_risks"]]:
            for risk in risk_list:
                if risk["type"] == risk_type:
                    count += 1
                    if risk["level"].value > max_level.value:
                        max_level = risk["level"]

        if max_level == RiskLevel.HIGH or count > 3:
            return "high"
        elif max_level == RiskLevel.MEDIUM or count > 1:
            return "medium"
        else:
            return "low"
