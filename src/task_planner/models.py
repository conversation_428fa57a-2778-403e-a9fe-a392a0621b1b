"""
Task Planning Data Models

This module provides comprehensive data structures for tasks, timelines, dependencies,
and planning outputs following the JSON schema from docs/design.md.
"""

import logging
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Optional

logger = logging.getLogger(__name__)


class TaskPriority(Enum):
    """Priority levels for tasks."""

    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


class TaskStatus(Enum):
    """Status of task execution."""

    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    BLOCKED = "blocked"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class TaskType(Enum):
    """Types of development tasks."""

    ANALYSIS = "analysis"
    DESIGN = "design"
    IMPLEMENTATION = "implementation"
    TESTING = "testing"
    DOCUMENTATION = "documentation"
    DEPLOYMENT = "deployment"
    REVIEW = "review"
    REFACTORING = "refactoring"


class RiskLevel(Enum):
    """Risk levels for tasks and dependencies."""

    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class DependencyType(Enum):
    """Types of task dependencies."""

    FINISH_TO_START = "finish_to_start"  # Task B starts after Task A finishes
    START_TO_START = "start_to_start"    # Task B starts when Task A starts
    FINISH_TO_FINISH = "finish_to_finish"  # Task B finishes when Task A finishes
    START_TO_FINISH = "start_to_finish"  # Task B finishes when Task A starts


@dataclass
class FileReference:
    """Reference to a specific file and location."""

    file_path: str
    lines: Optional[str] = None  # e.g., "120-200" or "10-30"
    description: Optional[str] = None


@dataclass
class TaskDependency:
    """Represents a dependency between tasks."""

    task_id: str
    dependency_type: DependencyType = DependencyType.FINISH_TO_START
    lag_hours: float = 0.0  # Delay after dependency is satisfied
    description: Optional[str] = None


@dataclass
class TaskRisk:
    """Risk associated with a task."""

    description: str
    level: RiskLevel
    probability: float  # 0.0 to 1.0
    impact: str
    mitigation: Optional[str] = None


@dataclass
class Task:
    """Represents a single development task."""

    id: str
    title: str
    description: str
    task_type: TaskType
    priority: TaskPriority = TaskPriority.MEDIUM
    status: TaskStatus = TaskStatus.NOT_STARTED
    estimate_hours: float = 0.0
    actual_hours: Optional[float] = None
    dependencies: list[TaskDependency] = field(default_factory=list)
    assignee: Optional[str] = None
    tags: list[str] = field(default_factory=list)
    files_to_change: list[FileReference] = field(default_factory=list)
    test_cases: list[str] = field(default_factory=list)
    acceptance_criteria: list[str] = field(default_factory=list)
    risks: list[TaskRisk] = field(default_factory=list)
    notes: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)

    def add_dependency(self, task_id: str, dependency_type: DependencyType = DependencyType.FINISH_TO_START, lag_hours: float = 0.0) -> None:
        """Add a dependency to this task."""
        dependency = TaskDependency(
            task_id=task_id,
            dependency_type=dependency_type,
            lag_hours=lag_hours
        )
        self.dependencies.append(dependency)
        self.updated_at = datetime.now()

    def add_risk(self, description: str, level: RiskLevel, probability: float, impact: str, mitigation: Optional[str] = None) -> None:
        """Add a risk to this task."""
        risk = TaskRisk(
            description=description,
            level=level,
            probability=probability,
            impact=impact,
            mitigation=mitigation
        )
        self.risks.append(risk)
        self.updated_at = datetime.now()

    def get_dependency_ids(self) -> list[str]:
        """Get list of task IDs this task depends on."""
        return [dep.task_id for dep in self.dependencies]

    def is_ready_to_start(self, completed_tasks: set[str]) -> bool:
        """Check if task is ready to start based on completed dependencies."""
        if self.status != TaskStatus.NOT_STARTED:
            return False
        
        for dependency in self.dependencies:
            if dependency.dependency_type == DependencyType.FINISH_TO_START:
                if dependency.task_id not in completed_tasks:
                    return False
        
        return True


@dataclass
class Milestone:
    """Represents a project milestone."""

    id: str
    title: str
    description: str
    target_date: datetime
    task_ids: list[str] = field(default_factory=list)
    is_completed: bool = False
    completion_date: Optional[datetime] = None


@dataclass
class Timeline:
    """Represents a project timeline with tasks and milestones."""

    title: str
    description: str
    start_date: datetime
    end_date: datetime
    tasks: list[Task] = field(default_factory=list)
    milestones: list[Milestone] = field(default_factory=list)
    total_estimated_hours: float = 0.0
    critical_path: list[str] = field(default_factory=list)  # Task IDs in critical path
    confidence: float = 0.0  # 0.0 to 1.0

    def add_task(self, task: Task) -> None:
        """Add a task to the timeline."""
        self.tasks.append(task)
        self.total_estimated_hours += task.estimate_hours

    def add_milestone(self, milestone: Milestone) -> None:
        """Add a milestone to the timeline."""
        self.milestones.append(milestone)

    def get_task_by_id(self, task_id: str) -> Optional[Task]:
        """Get a task by its ID."""
        for task in self.tasks:
            if task.id == task_id:
                return task
        return None

    def get_tasks_by_status(self, status: TaskStatus) -> list[Task]:
        """Get all tasks with a specific status."""
        return [task for task in self.tasks if task.status == status]

    def get_ready_tasks(self) -> list[Task]:
        """Get tasks that are ready to start."""
        completed_task_ids = {task.id for task in self.tasks if task.status == TaskStatus.COMPLETED}
        return [task for task in self.tasks if task.is_ready_to_start(completed_task_ids)]


@dataclass
class DependencyGraph:
    """Represents task dependencies as a graph."""

    nodes: dict[str, Task] = field(default_factory=dict)
    edges: dict[str, list[str]] = field(default_factory=dict)  # task_id -> [dependent_task_ids]
    critical_path: list[str] = field(default_factory=list)
    total_duration: float = 0.0

    def add_task(self, task: Task) -> None:
        """Add a task to the dependency graph."""
        self.nodes[task.id] = task
        if task.id not in self.edges:
            self.edges[task.id] = []

    def add_dependency(self, from_task_id: str, to_task_id: str) -> None:
        """Add a dependency edge from one task to another."""
        if from_task_id not in self.edges:
            self.edges[from_task_id] = []
        self.edges[from_task_id].append(to_task_id)

    def get_predecessors(self, task_id: str) -> list[str]:
        """Get tasks that must complete before this task can start."""
        predecessors = []
        for node_id, dependents in self.edges.items():
            if task_id in dependents:
                predecessors.append(node_id)
        return predecessors

    def get_successors(self, task_id: str) -> list[str]:
        """Get tasks that depend on this task."""
        return self.edges.get(task_id, [])

    def has_cycle(self) -> bool:
        """Check if the dependency graph has cycles."""
        visited = set()
        rec_stack = set()

        def dfs(node: str) -> bool:
            visited.add(node)
            rec_stack.add(node)

            for neighbor in self.edges.get(node, []):
                if neighbor not in visited:
                    if dfs(neighbor):
                        return True
                elif neighbor in rec_stack:
                    return True

            rec_stack.remove(node)
            return False

        for node in self.nodes:
            if node not in visited:
                if dfs(node):
                    return True
        return False


@dataclass
class TaskPlan:
    """Complete task plan with timeline and dependencies."""

    title: str
    summary: str
    timeline: Timeline
    dependency_graph: DependencyGraph
    tasks: list[Task] = field(default_factory=list)
    references: list[FileReference] = field(default_factory=list)
    confidence: float = 0.0
    methodology: str = "5-phase"  # Development methodology used
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)

    def to_dict(self) -> dict[str, Any]:
        """Convert task plan to dictionary format matching docs/design.md schema."""
        return {
            "title": self.title,
            "summary": self.summary,
            "tasks": [
                {
                    "id": task.id,
                    "title": task.title,
                    "description": task.description,
                    "type": task.task_type.value,
                    "priority": task.priority.value,
                    "estimate_hours": task.estimate_hours,
                    "dependencies": [dep.task_id for dep in task.dependencies],
                    "files_to_change": [
                        {"file": ref.file_path, "lines": ref.lines, "description": ref.description}
                        for ref in task.files_to_change
                    ],
                    "test_cases": task.test_cases,
                    "acceptance_criteria": task.acceptance_criteria,
                    "risks": [
                        {
                            "description": risk.description,
                            "level": risk.level.value,
                            "probability": risk.probability,
                            "impact": risk.impact,
                            "mitigation": risk.mitigation
                        }
                        for risk in task.risks
                    ]
                }
                for task in self.tasks
            ],
            "timeline": {
                "start_date": self.timeline.start_date.isoformat(),
                "end_date": self.timeline.end_date.isoformat(),
                "total_estimated_hours": self.timeline.total_estimated_hours,
                "critical_path": self.timeline.critical_path,
            },
            "references": [
                {"file": ref.file_path, "lines": ref.lines, "description": ref.description}
                for ref in self.references
            ],
            "confidence": self.confidence,
            "methodology": self.methodology,
            "created_at": self.created_at.isoformat(),
        }
