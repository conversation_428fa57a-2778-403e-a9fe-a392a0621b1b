"""
Requirement Breakdown Engine

This module provides algorithms to decompose complex requirements into actionable tasks,
analyze scope, and identify implementation steps following the 5-phase methodology.
"""

import logging
import re
from typing import Any

from .models import (
    FileReference,
    RiskLevel,
    Task,
    TaskPriority,
    TaskType,
)

logger = logging.getLogger(__name__)


class RequirementBreakdownEngine:
    """Breaks down complex requirements into actionable development tasks."""

    def __init__(self):
        """Initialize the requirement breakdown engine."""
        self.task_patterns = {
            # Analysis patterns
            "analysis": [
                r"analyz[e|ing]",
                r"research",
                r"investigate",
                r"understand",
                r"study",
                r"examine",
                r"assess",
                r"evaluate",
            ],
            # Design patterns
            "design": [
                r"design",
                r"architect",
                r"plan",
                r"model",
                r"structure",
                r"blueprint",
                r"specification",
                r"wireframe",
            ],
            # Implementation patterns
            "implementation": [
                r"implement",
                r"build",
                r"create",
                r"develop",
                r"code",
                r"write",
                r"add",
                r"integrate",
                r"connect",
            ],
            # Testing patterns
            "testing": [
                r"test",
                r"verify",
                r"validate",
                r"check",
                r"ensure",
                r"confirm",
                r"quality",
                r"coverage",
            ],
            # Documentation patterns
            "documentation": [
                r"document",
                r"readme",
                r"guide",
                r"manual",
                r"specification",
                r"comment",
                r"docstring",
            ],
            # Deployment patterns
            "deployment": [
                r"deploy",
                r"release",
                r"publish",
                r"launch",
                r"production",
                r"staging",
            ],
        }

        self.complexity_indicators = {
            "high": [
                r"complex",
                r"advanced",
                r"sophisticated",
                r"comprehensive",
                r"enterprise",
                r"scalable",
                r"distributed",
                r"microservice",
            ],
            "medium": [
                r"moderate",
                r"standard",
                r"typical",
                r"regular",
                r"normal",
                r"basic",
            ],
            "low": [
                r"simple",
                r"basic",
                r"minimal",
                r"straightforward",
                r"easy",
                r"quick",
            ],
        }

        self.priority_indicators = {
            "critical": [
                r"critical",
                r"urgent",
                r"emergency",
                r"blocker",
                r"must have",
                r"essential",
            ],
            "high": [
                r"important",
                r"high priority",
                r"should have",
                r"significant",
                r"major",
            ],
            "medium": [
                r"medium",
                r"moderate",
                r"could have",
                r"nice to have",
            ],
            "low": [
                r"low priority",
                r"minor",
                r"optional",
                r"future",
                r"enhancement",
            ],
        }

        logger.info("Initialized requirement breakdown engine")

    def break_down_requirement(self, requirement: str, context: dict[str, Any] = None) -> list[Task]:
        """Break down a single requirement into actionable tasks."""
        logger.info(f"Breaking down requirement: {requirement[:100]}...")

        if context is None:
            context = {}

        # Analyze the requirement
        analysis = self._analyze_requirement(requirement)

        # Generate tasks based on 5-phase methodology
        tasks = self._generate_5_phase_tasks(requirement, analysis, context)

        # Add specific implementation tasks
        implementation_tasks = self._generate_implementation_tasks(requirement, analysis, context)
        tasks.extend(implementation_tasks)

        # Add testing and documentation tasks
        quality_tasks = self._generate_quality_tasks(requirement, analysis, context)
        tasks.extend(quality_tasks)

        logger.info(f"Generated {len(tasks)} tasks from requirement breakdown")
        return tasks

    def break_down_requirements(self, requirements: list[str], context: dict[str, Any] = None) -> list[Task]:
        """Break down multiple requirements into a comprehensive task list."""
        logger.info(f"Breaking down {len(requirements)} requirements")

        all_tasks = []
        task_counter = 1

        for i, requirement in enumerate(requirements):
            # Add requirement context
            req_context = context.copy() if context else {}
            req_context["requirement_index"] = i
            req_context["total_requirements"] = len(requirements)

            # Break down individual requirement
            tasks = self.break_down_requirement(requirement, req_context)

            # Update task IDs to be unique across all requirements
            for task in tasks:
                task.id = f"T{task_counter:03d}"
                task_counter += 1

            all_tasks.extend(tasks)

        # Add cross-requirement integration tasks
        if len(requirements) > 1:
            integration_tasks = self._generate_integration_tasks(requirements, all_tasks, context)
            for task in integration_tasks:
                task.id = f"T{task_counter:03d}"
                task_counter += 1
            all_tasks.extend(integration_tasks)

        logger.info(f"Generated {len(all_tasks)} total tasks from {len(requirements)} requirements")
        return all_tasks

    def _analyze_requirement(self, requirement: str) -> dict[str, Any]:
        """Analyze a requirement to extract key characteristics."""
        requirement_lower = requirement.lower()

        analysis = {
            "task_types": [],
            "complexity": "medium",
            "priority": "medium",
            "estimated_effort": 8.0,  # Default 1 day
            "keywords": [],
            "entities": [],
            "risks": [],
        }

        # Identify task types
        for task_type, patterns in self.task_patterns.items():
            if any(re.search(pattern, requirement_lower) for pattern in patterns):
                analysis["task_types"].append(task_type)

        # Determine complexity
        for complexity, indicators in self.complexity_indicators.items():
            if any(re.search(indicator, requirement_lower) for indicator in indicators):
                analysis["complexity"] = complexity
                break

        # Determine priority
        for priority, indicators in self.priority_indicators.items():
            if any(re.search(indicator, requirement_lower) for indicator in indicators):
                analysis["priority"] = priority
                break

        # Extract keywords
        analysis["keywords"] = self._extract_keywords(requirement)

        # Extract entities (files, components, etc.)
        analysis["entities"] = self._extract_entities(requirement)

        # Estimate effort based on complexity and task types
        analysis["estimated_effort"] = self._estimate_effort(analysis)

        # Identify potential risks
        analysis["risks"] = self._identify_risks(requirement, analysis)

        return analysis

    def _generate_5_phase_tasks(self, requirement: str, analysis: dict[str, Any], context: dict[str, Any]) -> list[Task]:
        """Generate tasks following the 5-phase methodology."""
        tasks = []
        base_id = context.get("requirement_index", 0) * 100

        # Phase 1: Discovery & Analysis
        if "analysis" in analysis["task_types"] or analysis["complexity"] in ["medium", "high"]:
            tasks.append(Task(
                id=f"T{base_id + 1:03d}",
                title=f"Discovery & Analysis: {requirement[:50]}...",
                description=f"Analyze requirements, understand current state, and define scope for: {requirement}",
                task_type=TaskType.ANALYSIS,
                priority=TaskPriority(analysis["priority"]),
                estimate_hours=analysis["estimated_effort"] * 0.2,  # 20% of total effort
                acceptance_criteria=[
                    "Requirements are clearly understood and documented",
                    "Current state analysis is complete",
                    "Scope and constraints are defined",
                    "Success criteria are established",
                ],
                tags=["discovery", "analysis", "phase-1"],
            ))

        # Phase 2: Task Planning
        tasks.append(Task(
            id=f"T{base_id + 2:03d}",
            title=f"Task Planning: {requirement[:50]}...",
            description=f"Break down requirements into detailed tasks and create implementation plan for: {requirement}",
            task_type=TaskType.DESIGN,
            priority=TaskPriority(analysis["priority"]),
            estimate_hours=analysis["estimated_effort"] * 0.15,  # 15% of total effort
            acceptance_criteria=[
                "Detailed task breakdown is complete",
                "Dependencies are identified",
                "Timeline and milestones are defined",
                "Resource requirements are estimated",
            ],
            tags=["planning", "design", "phase-2"],
        ))

        # Phase 3: Implementation
        if "implementation" in analysis["task_types"] or "design" in analysis["task_types"]:
            tasks.append(Task(
                id=f"T{base_id + 3:03d}",
                title=f"Implementation: {requirement[:50]}...",
                description=f"Execute the implementation plan for: {requirement}",
                task_type=TaskType.IMPLEMENTATION,
                priority=TaskPriority(analysis["priority"]),
                estimate_hours=analysis["estimated_effort"] * 0.5,  # 50% of total effort
                acceptance_criteria=[
                    "Core functionality is implemented",
                    "Code follows project standards",
                    "Integration points are working",
                    "Error handling is in place",
                ],
                tags=["implementation", "coding", "phase-3"],
            ))

        # Phase 4: Verification
        if "testing" in analysis["task_types"] or analysis["complexity"] in ["medium", "high"]:
            tasks.append(Task(
                id=f"T{base_id + 4:03d}",
                title=f"Verification: {requirement[:50]}...",
                description=f"Test and validate the implementation for: {requirement}",
                task_type=TaskType.TESTING,
                priority=TaskPriority(analysis["priority"]),
                estimate_hours=analysis["estimated_effort"] * 0.1,  # 10% of total effort
                acceptance_criteria=[
                    "Unit tests are written and passing",
                    "Integration tests are complete",
                    "Requirements are verified",
                    "Quality gates are met",
                ],
                tags=["testing", "verification", "phase-4"],
            ))

        # Phase 5: Documentation & Handover
        if "documentation" in analysis["task_types"] or analysis["complexity"] in ["medium", "high"]:
            tasks.append(Task(
                id=f"T{base_id + 5:03d}",
                title=f"Documentation & Handover: {requirement[:50]}...",
                description=f"Document the implementation and prepare for handover: {requirement}",
                task_type=TaskType.DOCUMENTATION,
                priority=TaskPriority(analysis["priority"]),
                estimate_hours=analysis["estimated_effort"] * 0.05,  # 5% of total effort
                acceptance_criteria=[
                    "Implementation is documented",
                    "User guides are updated",
                    "Knowledge transfer is complete",
                    "Maintenance procedures are documented",
                ],
                tags=["documentation", "handover", "phase-5"],
            ))

        # Add dependencies between phases
        for i in range(1, len(tasks)):
            tasks[i].add_dependency(tasks[i-1].id)

        return tasks

    def _generate_implementation_tasks(self, requirement: str, analysis: dict[str, Any], context: dict[str, Any]) -> list[Task]:
        """Generate specific implementation tasks based on requirement analysis."""
        tasks = []
        base_id = context.get("requirement_index", 0) * 100 + 10

        # Generate tasks based on identified entities and keywords
        entities = analysis.get("entities", [])
        keywords = analysis.get("keywords", [])

        task_id = base_id
        for entity in entities:
            if entity.get("type") == "component":
                tasks.append(Task(
                    id=f"T{task_id:03d}",
                    title=f"Implement {entity['name']} component",
                    description=f"Create and implement the {entity['name']} component as part of: {requirement}",
                    task_type=TaskType.IMPLEMENTATION,
                    priority=TaskPriority(analysis["priority"]),
                    estimate_hours=analysis["estimated_effort"] * 0.3,
                    files_to_change=[FileReference(file_path=f"src/{entity['name'].lower()}.py")],
                    tags=["component", "implementation"],
                ))
                task_id += 1

            elif entity.get("type") == "api":
                tasks.append(Task(
                    id=f"T{task_id:03d}",
                    title=f"Implement {entity['name']} API",
                    description=f"Create API endpoints for {entity['name']} as part of: {requirement}",
                    task_type=TaskType.IMPLEMENTATION,
                    priority=TaskPriority(analysis["priority"]),
                    estimate_hours=analysis["estimated_effort"] * 0.25,
                    files_to_change=[FileReference(file_path=f"src/api/{entity['name'].lower()}.py")],
                    tags=["api", "implementation"],
                ))
                task_id += 1

        return tasks

    def _generate_quality_tasks(self, requirement: str, analysis: dict[str, Any], context: dict[str, Any]) -> list[Task]:
        """Generate testing and documentation tasks."""
        tasks = []
        base_id = context.get("requirement_index", 0) * 100 + 50

        # Unit testing task
        tasks.append(Task(
            id=f"T{base_id + 1:03d}",
            title=f"Unit Tests: {requirement[:50]}...",
            description=f"Write comprehensive unit tests for: {requirement}",
            task_type=TaskType.TESTING,
            priority=TaskPriority(analysis["priority"]),
            estimate_hours=analysis["estimated_effort"] * 0.15,
            acceptance_criteria=[
                "Unit tests cover all new functionality",
                "Test coverage is >80%",
                "All tests pass",
                "Edge cases are tested",
            ],
            tags=["unit-tests", "testing"],
        ))

        # Integration testing task
        if analysis["complexity"] in ["medium", "high"]:
            tasks.append(Task(
                id=f"T{base_id + 2:03d}",
                title=f"Integration Tests: {requirement[:50]}...",
                description=f"Write integration tests for: {requirement}",
                task_type=TaskType.TESTING,
                priority=TaskPriority(analysis["priority"]),
                estimate_hours=analysis["estimated_effort"] * 0.1,
                acceptance_criteria=[
                    "Integration tests verify component interactions",
                    "End-to-end workflows are tested",
                    "All integration tests pass",
                ],
                tags=["integration-tests", "testing"],
            ))

        return tasks

    def _generate_integration_tasks(self, requirements: list[str], tasks: list[Task], context: dict[str, Any]) -> list[Task]:
        """Generate tasks for integrating multiple requirements."""
        integration_tasks = []

        if len(requirements) > 1:
            integration_tasks.append(Task(
                id="T900",
                title="Cross-Requirement Integration",
                description="Integrate and test interactions between multiple requirements",
                task_type=TaskType.IMPLEMENTATION,
                priority=TaskPriority.HIGH,
                estimate_hours=len(requirements) * 2.0,
                acceptance_criteria=[
                    "All requirements work together seamlessly",
                    "No conflicts between implementations",
                    "Integration points are tested",
                    "Performance is acceptable",
                ],
                tags=["integration", "cross-requirement"],
            ))

        return integration_tasks

    def _extract_keywords(self, requirement: str) -> list[str]:
        """Extract important keywords from the requirement."""
        # Simple keyword extraction - could be enhanced with NLP
        words = re.findall(r'\b\w+\b', requirement.lower())
        
        # Filter out common words
        stop_words = {"the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by"}
        keywords = [word for word in words if word not in stop_words and len(word) > 3]
        
        return list(set(keywords))  # Remove duplicates

    def _extract_entities(self, requirement: str) -> list[dict[str, str]]:
        """Extract entities like components, APIs, files from the requirement."""
        entities = []
        
        # Look for component mentions
        component_patterns = [
            r"(\w+)\s+component",
            r"(\w+)\s+service",
            r"(\w+)\s+module",
            r"(\w+)\s+class",
        ]
        
        for pattern in component_patterns:
            matches = re.finditer(pattern, requirement, re.IGNORECASE)
            for match in matches:
                entities.append({
                    "name": match.group(1),
                    "type": "component"
                })
        
        # Look for API mentions
        api_patterns = [
            r"(\w+)\s+API",
            r"(\w+)\s+endpoint",
            r"(\w+)\s+service",
        ]
        
        for pattern in api_patterns:
            matches = re.finditer(pattern, requirement, re.IGNORECASE)
            for match in matches:
                entities.append({
                    "name": match.group(1),
                    "type": "api"
                })
        
        return entities

    def _estimate_effort(self, analysis: dict[str, Any]) -> float:
        """Estimate effort in hours based on analysis."""
        base_effort = 8.0  # 1 day default
        
        # Adjust based on complexity
        complexity_multipliers = {
            "low": 0.5,
            "medium": 1.0,
            "high": 2.0,
        }
        
        effort = base_effort * complexity_multipliers.get(analysis["complexity"], 1.0)
        
        # Adjust based on number of task types
        effort *= (1 + len(analysis["task_types"]) * 0.2)
        
        return effort

    def _identify_risks(self, requirement: str, analysis: dict[str, Any]) -> list[dict[str, Any]]:
        """Identify potential risks in the requirement."""
        risks = []
        
        # High complexity risk
        if analysis["complexity"] == "high":
            risks.append({
                "description": "High complexity may lead to implementation challenges",
                "level": RiskLevel.MEDIUM,
                "probability": 0.6,
                "impact": "Potential delays and increased effort",
                "mitigation": "Break down into smaller tasks and conduct thorough analysis"
            })
        
        # Multiple task types risk
        if len(analysis["task_types"]) > 3:
            risks.append({
                "description": "Multiple task types may indicate scope creep",
                "level": RiskLevel.MEDIUM,
                "probability": 0.4,
                "impact": "Unclear requirements and extended timeline",
                "mitigation": "Clarify scope and prioritize core functionality"
            })
        
        # Integration risk
        if "integration" in requirement.lower():
            risks.append({
                "description": "Integration tasks have higher failure risk",
                "level": RiskLevel.HIGH,
                "probability": 0.5,
                "impact": "System compatibility issues",
                "mitigation": "Thorough testing and incremental integration"
            })
        
        return risks
