"""
Orchestrator Agent

This module provides the main orchestrator agent that coordinates workflow
execution and manages multi-agent interactions.
"""

import asyncio
import logging
import time
from typing import Any

from ..agents.base import (
    Agent,
    AgentContext,
    AgentResponse,
    AgentType,
    Message,
    MessageRole,
)
from ..agents.context import ConversationContextManager
from ..agents.exceptions import AgentTimeoutError
from ..agents.formatters import MarkdownFormatter
from ..agents.llm_client import LLMClient
from ..config import Settings
from .classifier import QueryClassifier
from .router import AgentRouter
from .synthesizer import ResponseSynthesizer

logger = logging.getLogger(__name__)


class OrchestratorAgent(Agent):
    """Main orchestrator agent that coordinates multi-agent workflows."""

    def __init__(
        self,
        settings: Settings,
        llm_client: LLMClient,
        formatter: MarkdownFormatter,
        context_manager: ConversationContextManager,
        agent_factory: Any,  # Avoid circular import
    ):
        super().__init__(AgentType.ORCHESTRATOR, settings)
        self.llm_client = llm_client
        self.formatter = formatter
        self.context_manager = context_manager
        self.agent_factory = agent_factory

        # Initialize orchestrator components
        self.classifier = QueryClassifier()
        self.router = AgentRouter(self.classifier)
        self.synthesizer = ResponseSynthesizer(formatter)

        # Configuration
        self.max_agent_timeout = 30.0
        self.max_fallback_attempts = 3
        self.enable_multi_agent = True

        # Agent registry
        self._agent_cache: dict[AgentType, Agent] = {}

        logger.info("Initialized Orchestrator Agent")

    def can_handle_query(self, query: str, context: AgentContext) -> float:
        """Orchestrator can handle any query (it routes to appropriate agents)."""
        return 1.0  # Always confident - orchestrator is the entry point

    async def process_query(self, query: str, context: AgentContext) -> AgentResponse:
        """Process a query by routing to appropriate agents and synthesizing responses."""
        start_time = time.time()

        try:
            logger.info(f"Orchestrator processing query: {query[:100]}...")

            # Update conversation context
            await self._update_conversation_context(query, context)

            # Determine available agents
            available_agents = self._get_available_agents()

            # Route the query
            routing_decision = self.router.route_query(query, context, available_agents)

            # Execute the routing decision
            responses = await self._execute_routing_decision(query, context, routing_decision)

            # Synthesize responses
            synthesis_result = self.synthesizer.synthesize_responses(
                responses, routing_decision.routing_strategy, query
            )

            processing_time = time.time() - start_time

            # Create orchestrator response
            orchestrator_response = AgentResponse(
                agent_type=self.agent_type,
                content=synthesis_result.content,
                confidence=synthesis_result.confidence,
                sources=synthesis_result.sources,
                processing_time=processing_time,
                metadata={
                    "routing_strategy": routing_decision.routing_strategy,
                    "primary_agent": routing_decision.primary_agent.value,
                    "secondary_agents": [a.value for a in routing_decision.secondary_agents],
                    "synthesis_strategy": synthesis_result.synthesis_strategy,
                    "agent_count": len(responses),
                    **synthesis_result.metadata,
                },
            )

            # Update conversation context with response
            await self._update_conversation_context_with_response(orchestrator_response, context)

            self._update_stats(processing_time, success=True)
            logger.info(f"Orchestrator completed in {processing_time:.2f}s")

            return orchestrator_response

        except Exception as e:
            processing_time = time.time() - start_time
            self._update_stats(processing_time, success=False)

            logger.error(f"Orchestrator failed: {e}")

            # Try to provide a fallback response
            fallback_response = await self._generate_fallback_response(query, context, e)
            return fallback_response

    async def _execute_routing_decision(
        self, query: str, context: AgentContext, routing_decision: Any
    ) -> list[AgentResponse]:
        """Execute the routing decision and collect responses."""
        responses = []

        try:
            # Execute primary agent
            primary_agent = self._get_agent(routing_decision.primary_agent)
            primary_response = await self._execute_agent_with_timeout(primary_agent, query, context)
            responses.append(primary_response)

            # Execute secondary agents if any
            if routing_decision.secondary_agents and self.enable_multi_agent:
                secondary_tasks = []
                for agent_type in routing_decision.secondary_agents:
                    agent = self._get_agent(agent_type)
                    task = self._execute_agent_with_timeout(agent, query, context)
                    secondary_tasks.append(task)

                # Wait for secondary agents with timeout
                try:
                    secondary_responses = await asyncio.wait_for(
                        asyncio.gather(*secondary_tasks, return_exceptions=True),
                        timeout=self.max_agent_timeout,
                    )

                    # Add successful secondary responses
                    for response in secondary_responses:
                        if isinstance(response, AgentResponse):
                            responses.append(response)
                        else:
                            logger.warning(f"Secondary agent failed: {response}")

                except TimeoutError:
                    logger.warning("Secondary agents timed out")

            # If primary agent failed, try fallback agents
            if not responses or responses[0].confidence < 0.3:
                fallback_response = await self._try_fallback_agents(query, context, routing_decision.fallback_agents)
                if fallback_response:
                    responses.append(fallback_response)

            return responses

        except Exception as e:
            logger.error(f"Failed to execute routing decision: {e}")
            # Try fallback agents as last resort
            fallback_response = await self._try_fallback_agents(query, context, routing_decision.fallback_agents)
            return [fallback_response] if fallback_response else []

    async def _execute_agent_with_timeout(self, agent: Agent, query: str, context: AgentContext) -> AgentResponse:
        """Execute an agent with timeout protection."""
        try:
            return await agent._execute_with_timeout(agent.process_query(query, context), self.max_agent_timeout)
        except AgentTimeoutError:
            logger.warning(f"Agent {agent.agent_type.value} timed out")
            raise
        except Exception as e:
            logger.error(f"Agent {agent.agent_type.value} failed: {e}")
            raise

    async def _try_fallback_agents(
        self, query: str, context: AgentContext, fallback_agents: list[AgentType]
    ) -> AgentResponse | None:
        """Try fallback agents in order."""
        for agent_type in fallback_agents[: self.max_fallback_attempts]:
            try:
                agent = self._get_agent(agent_type)
                response = await self._execute_agent_with_timeout(agent, query, context)
                logger.info(f"Fallback agent {agent_type.value} succeeded")
                return response
            except Exception as e:
                logger.warning(f"Fallback agent {agent_type.value} failed: {e}")
                continue

        return None

    def _get_agent(self, agent_type: AgentType) -> Agent:
        """Get or create an agent instance."""
        if agent_type not in self._agent_cache:
            self._agent_cache[agent_type] = self.agent_factory.create_agent(agent_type)
        return self._agent_cache[agent_type]

    def _get_available_agents(self) -> set[AgentType]:
        """Get set of available agent types."""
        # For now, all agents are available
        # In the future, this could check agent health, load, etc.
        return {
            AgentType.TECHNICAL_ARCHITECT,
            AgentType.TASK_PLANNER,
            AgentType.RAG_RETRIEVAL,
        }

    async def _update_conversation_context(self, query: str, context: AgentContext) -> None:
        """Update conversation context with the new query."""
        try:
            session_id = context.conversation_context.session_id
            message = Message(role=MessageRole.USER, content=query)
            await self.context_manager.add_message(session_id, message)
        except Exception as e:
            logger.warning(f"Failed to update conversation context: {e}")

    async def _update_conversation_context_with_response(self, response: AgentResponse, context: AgentContext) -> None:
        """Update conversation context with the response."""
        try:
            session_id = context.conversation_context.session_id
            message = Message(
                role=MessageRole.ASSISTANT,
                content=response.content,
                metadata={
                    "agent_type": response.agent_type.value,
                    "confidence": response.confidence,
                    "sources": response.sources,
                },
            )
            await self.context_manager.add_message(session_id, message)
        except Exception as e:
            logger.warning(f"Failed to update conversation context with response: {e}")

    async def _generate_fallback_response(self, query: str, context: AgentContext, error: Exception) -> AgentResponse:
        """Generate a fallback response when all else fails."""
        error_message = (
            f"I apologize, but I encountered an error while processing your query: '{query}'\n\n"
            f"Error details: {error!s}\n\n"
            f"Please try rephrasing your question or contact support if the issue persists."
        )

        return AgentResponse(
            agent_type=self.agent_type,
            content=error_message,
            confidence=0.1,
            sources=[],
            metadata={"error_type": type(error).__name__, "fallback_response": True},
        )

    def get_orchestrator_stats(self) -> dict[str, Any]:
        """Get orchestrator-specific statistics."""
        base_stats = self.get_stats()

        orchestrator_stats = {
            "classifier_stats": self.classifier.get_classification_stats(),
            "router_stats": self.router.get_routing_stats(),
            "synthesizer_stats": self.synthesizer.get_synthesis_stats(),
            "cached_agents": list(self._agent_cache.keys()),
            "configuration": {
                "max_agent_timeout": self.max_agent_timeout,
                "max_fallback_attempts": self.max_fallback_attempts,
                "enable_multi_agent": self.enable_multi_agent,
            },
        }

        return {**base_stats, **orchestrator_stats}
