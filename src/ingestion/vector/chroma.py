"""
ChromaDB Vector Store Implementation

This module provides ChromaDB integration for vector storage and retrieval
with persistence, backup, and advanced search capabilities.
"""

import logging
import uuid
from typing import List, Dict, Any, Optional
from pathlib import Path
import chromadb
from chromadb.config import Settings as ChromaSettings

from ..base import (
    VectorStore,
    SearchResult,
    EmbeddedChunk,
    EmbeddingMetadata,
    Chunk,
    ChunkType,
    ChunkContext,
    FileMetadata,
)
from ..exceptions import IngestionError
from ...config import Settings

logger = logging.getLogger(__name__)


class ChromaVectorStore(VectorStore):
    """ChromaDB implementation of vector store."""

    def __init__(self, settings: Settings):
        """Initialize ChromaDB vector store."""
        self.settings = settings
        self.collection_name = settings.chroma_collection_name
        self.persist_directory = Path(settings.chroma_persist_directory)

        # Ensure persist directory exists
        self.persist_directory.mkdir(parents=True, exist_ok=True)

        # Initialize ChromaDB client
        self._client = None
        self._collection = None

        # Statistics
        self._stats = {
            "total_embeddings": 0,
            "total_searches": 0,
            "failed_operations": 0,
            "collection_created": False,
        }

        logger.info(f"Initialized ChromaDB vector store with collection '{self.collection_name}'")

    def _get_client(self):
        """Get or create ChromaDB client."""
        if self._client is None:
            try:
                # Configure ChromaDB settings
                chroma_settings = ChromaSettings(
                    persist_directory=str(self.persist_directory),
                    anonymized_telemetry=False,
                )

                # Create client
                self._client = chromadb.PersistentClient(path=str(self.persist_directory), settings=chroma_settings)

                logger.info(f"Connected to ChromaDB at {self.persist_directory}")

            except Exception as e:
                raise IngestionError(f"Failed to connect to ChromaDB: {e}", operation="connect_chromadb", cause=e)

        return self._client

    def _get_collection(self):
        """Get or create collection."""
        if self._collection is None:
            client = self._get_client()

            try:
                # Try to get existing collection
                self._collection = client.get_collection(name=self.collection_name)
                logger.info(f"Connected to existing collection '{self.collection_name}'")

            except Exception:
                # Collection doesn't exist, create it
                try:
                    self._collection = client.create_collection(
                        name=self.collection_name, metadata={"description": "Codebase embeddings for RAG system"}
                    )
                    self._stats["collection_created"] = True
                    logger.info(f"Created new collection '{self.collection_name}'")

                except Exception as e:
                    raise IngestionError(
                        f"Failed to create collection '{self.collection_name}': {e}",
                        operation="create_collection",
                        cause=e,
                    )

        return self._collection

    async def add_embeddings(
        self, embeddings: List[List[float]], metadata: List[Dict[str, Any]], ids: List[str]
    ) -> bool:
        """Add embeddings with metadata to the store."""
        # Check for mismatched lengths first
        if not (len(embeddings) == len(metadata) == len(ids)):
            raise IngestionError(
                "Embeddings, metadata, and IDs must have the same length",
                details={"embeddings_count": len(embeddings), "metadata_count": len(metadata), "ids_count": len(ids)},
            )

        # Early return for empty inputs (after length validation)
        if not embeddings:
            return True

        try:
            collection = self._get_collection()

            # Extract documents (content) from metadata
            documents = []
            processed_metadata = []

            for meta in metadata:
                # Extract content for ChromaDB document storage
                content = meta.get("content", "")
                documents.append(content)

                # Clean metadata for ChromaDB (remove content to avoid duplication)
                clean_meta = {k: v for k, v in meta.items() if k != "content"}

                # Convert complex objects to strings for ChromaDB compatibility
                for key, value in clean_meta.items():
                    if isinstance(value, (dict, list)):
                        clean_meta[key] = str(value)
                    elif value is None:
                        clean_meta[key] = ""

                processed_metadata.append(clean_meta)

            # Add to collection
            collection.add(embeddings=embeddings, documents=documents, metadatas=processed_metadata, ids=ids)

            self._stats["total_embeddings"] += len(embeddings)
            logger.debug(f"Added {len(embeddings)} embeddings to collection '{self.collection_name}'")

            return True

        except Exception as e:
            self._stats["failed_operations"] += 1
            logger.error(f"Failed to add embeddings to ChromaDB: {e}")
            raise IngestionError(f"Failed to add embeddings: {e}", operation="add_embeddings", cause=e)

    async def search_similar(
        self, query_embedding: List[float], top_k: int = 10, filters: Optional[Dict[str, Any]] = None
    ) -> List[SearchResult]:
        """Search for similar embeddings."""
        try:
            collection = self._get_collection()

            # Prepare where clause for filtering
            where_clause = None
            if filters:
                where_clause = self._build_where_clause(filters)

            # Perform search
            results = collection.query(
                query_embeddings=[query_embedding],
                n_results=top_k,
                where=where_clause,
                include=["embeddings", "documents", "metadatas", "distances"],
            )

            self._stats["total_searches"] += 1

            # Convert results to SearchResult objects
            search_results = []

            if results["ids"] and results["ids"][0]:  # Check if we have results
                for i, (id_, distance, document, metadata, embedding) in enumerate(
                    zip(
                        results["ids"][0],
                        results["distances"][0],
                        results["documents"][0],
                        results["metadatas"][0],
                        results["embeddings"][0] if results["embeddings"] else [None] * len(results["ids"][0]),
                    )
                ):
                    # Convert distance to similarity score (ChromaDB uses cosine distance)
                    similarity_score = 1.0 - distance

                    # Reconstruct EmbeddedChunk from stored data
                    embedded_chunk = self._reconstruct_embedded_chunk(id_, document, metadata, embedding)

                    search_result = SearchResult(
                        embedded_chunk=embedded_chunk, similarity_score=similarity_score, rank=i + 1
                    )

                    search_results.append(search_result)

            logger.debug(f"Found {len(search_results)} similar embeddings")
            return search_results

        except Exception as e:
            self._stats["failed_operations"] += 1
            logger.error(f"Failed to search ChromaDB: {e}")
            raise IngestionError(f"Failed to search embeddings: {e}", operation="search_similar", cause=e)

    def _build_where_clause(self, filters: Dict[str, Any]) -> Dict[str, Any]:
        """Build ChromaDB where clause from filters."""
        where_clause = {}

        for key, value in filters.items():
            if isinstance(value, list):
                # Handle list values with $in operator
                where_clause[key] = {"$in": value}
            elif isinstance(value, dict):
                # Handle range queries
                where_clause[key] = value
            else:
                # Handle exact matches
                where_clause[key] = value

        return where_clause

    def _reconstruct_embedded_chunk(
        self, id_: str, document: str, metadata: Dict[str, Any], embedding: Optional[List[float]]
    ) -> EmbeddedChunk:
        """Reconstruct EmbeddedChunk from stored data."""
        # Reconstruct FileMetadata
        file_metadata = FileMetadata(
            file_path=metadata.get("file_path", ""),
            file_size=int(metadata.get("file_size", 0)),
            file_type=metadata.get("file_type", "text"),
            last_modified=None,  # Not stored in metadata
            language=metadata.get("language"),
            priority=float(metadata.get("priority", 0.0)),
        )

        # Reconstruct ChunkContext
        context_data = metadata.get("context", {})
        if isinstance(context_data, str):
            # If context was stringified, we can't fully reconstruct it
            context = ChunkContext()
        else:
            context = ChunkContext(
                function_name=context_data.get("function_name"),
                class_name=context_data.get("class_name"),
                module_name=context_data.get("module_name"),
                section_headers=context_data.get("section_headers", []),
            )

        # Reconstruct Chunk
        chunk = Chunk(
            content=document,
            chunk_id=metadata.get("chunk_id", id_.replace("emb_", "")),
            chunk_type=ChunkType(metadata.get("chunk_type", "generic")),
            file_metadata=file_metadata,
            start_line=int(metadata.get("start_line", 1)),
            end_line=int(metadata.get("end_line", 1)),
            context=context,
            token_count=int(metadata.get("token_count", 0)) if metadata.get("token_count") else None,
            chunking_strategy=metadata.get("chunking_strategy"),
        )

        # Reconstruct EmbeddingMetadata
        embedding_metadata = EmbeddingMetadata(
            embedding_model=metadata.get("embedding_model", "unknown"),
            embedding_dimension=int(metadata.get("embedding_dimension", 0)),
            embedding_provider=metadata.get("embedding_provider", "unknown"),
        )

        # Create EmbeddedChunk
        embedded_chunk = EmbeddedChunk(chunk=chunk, embedding=embedding or [], embedding_metadata=embedding_metadata)

        return embedded_chunk

    async def delete_embeddings(self, ids: List[str]) -> bool:
        """Delete embeddings by their IDs."""
        if not ids:
            return True

        try:
            collection = self._get_collection()
            collection.delete(ids=ids)

            logger.debug(f"Deleted {len(ids)} embeddings from collection '{self.collection_name}'")
            return True

        except Exception as e:
            self._stats["failed_operations"] += 1
            logger.error(f"Failed to delete embeddings from ChromaDB: {e}")
            raise IngestionError(f"Failed to delete embeddings: {e}", operation="delete_embeddings", cause=e)

    async def get_embedding(self, embedding_id: str) -> Optional[EmbeddedChunk]:
        """Get a specific embedding by ID."""
        try:
            collection = self._get_collection()

            results = collection.get(ids=[embedding_id], include=["embeddings", "documents", "metadatas"])

            if not results["ids"] or not results["ids"]:
                return None

            # Reconstruct EmbeddedChunk
            id_ = results["ids"][0]
            document = results["documents"][0] if results["documents"] else ""
            metadata = results["metadatas"][0] if results["metadatas"] else {}
            embedding = results["embeddings"][0] if results["embeddings"] else []

            return self._reconstruct_embedded_chunk(id_, document, metadata, embedding)

        except Exception as e:
            logger.error(f"Failed to get embedding {embedding_id} from ChromaDB: {e}")
            raise IngestionError(f"Failed to get embedding: {e}", operation="get_embedding", cause=e)

    async def get_collection_info(self) -> Dict[str, Any]:
        """Get information about the collection."""
        try:
            collection = self._get_collection()

            # Get collection count
            count = collection.count()

            # Get collection metadata
            collection_metadata = collection.metadata or {}

            return {
                "name": self.collection_name,
                "count": count,
                "metadata": collection_metadata,
                "persist_directory": str(self.persist_directory),
                "stats": self._stats.copy(),
            }

        except Exception as e:
            logger.error(f"Failed to get collection info: {e}")
            raise IngestionError(f"Failed to get collection info: {e}", operation="get_collection_info", cause=e)

    async def create_collection(self, collection_name: str) -> bool:
        """Create a new collection."""
        try:
            client = self._get_client()

            collection = client.create_collection(
                name=collection_name, metadata={"description": "Codebase embeddings for RAG system"}
            )

            logger.info(f"Created collection '{collection_name}'")
            return True

        except Exception as e:
            logger.error(f"Failed to create collection '{collection_name}': {e}")
            raise IngestionError(f"Failed to create collection: {e}", operation="create_collection", cause=e)

    async def delete_collection(self, collection_name: str) -> bool:
        """Delete a collection."""
        try:
            client = self._get_client()
            client.delete_collection(name=collection_name)

            # Reset collection reference if it's the current one
            if collection_name == self.collection_name:
                self._collection = None

            logger.info(f"Deleted collection '{collection_name}'")
            return True

        except Exception as e:
            logger.error(f"Failed to delete collection '{collection_name}': {e}")
            raise IngestionError(f"Failed to delete collection: {e}", operation="delete_collection", cause=e)

    def get_stats(self) -> Dict[str, Any]:
        """Get vector store statistics."""
        return self._stats.copy()

    def reset_stats(self) -> None:
        """Reset statistics."""
        self._stats = {
            "total_embeddings": 0,
            "total_searches": 0,
            "failed_operations": 0,
            "collection_created": self._stats["collection_created"],
        }

    async def backup_collection(self, backup_path: Optional[Path] = None) -> bool:
        """Backup the collection data."""
        try:
            if backup_path is None:
                backup_path = self.persist_directory / f"{self.collection_name}_backup"

            # ChromaDB automatically persists data, so we just need to copy the directory
            import shutil

            if backup_path.exists():
                shutil.rmtree(backup_path)

            shutil.copytree(self.persist_directory, backup_path)

            logger.info(f"Backed up collection to {backup_path}")
            return True

        except Exception as e:
            logger.error(f"Failed to backup collection: {e}")
            raise IngestionError(f"Failed to backup collection: {e}", operation="backup_collection", cause=e)
