"""
Vector Store Factory

This module provides a factory for creating vector store instances based on
configuration settings with support for multiple providers.
"""

import logging
from typing import Dict, Any, List

from ..base import VectorStore
from ..exceptions import IngestionError
from ...config import Settings

logger = logging.getLogger(__name__)


class VectorStoreFactory:
    """Factory for creating vector store instances."""

    @staticmethod
    def create_vector_store(settings: Settings) -> VectorStore:
        """Create a vector store based on settings."""
        provider = settings.vector_store_provider.lower()

        if provider == "chromadb":
            from .chroma import ChromaVectorStore

            return ChromaVectorStore(settings)
        elif provider == "weaviate":
            # Future implementation
            raise IngestionError(
                "Weaviate vector store is not yet implemented", details={"supported_providers": ["chromadb"]}
            )
        elif provider == "pinecone":
            # Future implementation
            raise IngestionError(
                "Pinecone vector store is not yet implemented", details={"supported_providers": ["chromadb"]}
            )
        else:
            raise IngestionError(
                f"Unsupported vector store provider: {provider}",
                details={"supported_providers": VectorStoreFactory.get_available_providers()},
            )

    @staticmethod
    def get_available_providers() -> List[str]:
        """Get list of available vector store providers."""
        return ["chromadb"]  # Add more as they're implemented

    @staticmethod
    def validate_provider_config(settings: Settings) -> bool:
        """Validate provider configuration."""
        provider = settings.vector_store_provider.lower()

        if provider == "chromadb":
            # Validate ChromaDB configuration
            if not settings.chroma_collection_name:
                raise IngestionError("ChromaDB collection name is required")

            if not settings.chroma_persist_directory:
                raise IngestionError("ChromaDB persist directory is required")

            return True
        else:
            return False

    @staticmethod
    def get_provider_info(provider: str) -> Dict[str, Any]:
        """Get information about a specific provider."""
        provider_info = {
            "chromadb": {
                "name": "ChromaDB",
                "description": "Open-source embedding database",
                "features": [
                    "Local persistence",
                    "Cosine similarity search",
                    "Metadata filtering",
                    "Collection management",
                ],
                "requirements": ["chromadb"],
                "config_keys": ["chroma_host", "chroma_port", "chroma_collection_name", "chroma_persist_directory"],
            },
            "weaviate": {
                "name": "Weaviate",
                "description": "Cloud-native vector database",
                "features": ["GraphQL API", "Hybrid search", "Multi-tenancy", "Automatic vectorization"],
                "requirements": ["weaviate-client"],
                "config_keys": ["weaviate_url", "weaviate_api_key", "weaviate_class_name"],
                "status": "planned",
            },
            "pinecone": {
                "name": "Pinecone",
                "description": "Managed vector database service",
                "features": ["Serverless scaling", "Real-time updates", "Metadata filtering", "High performance"],
                "requirements": ["pinecone-client"],
                "config_keys": ["pinecone_api_key", "pinecone_environment", "pinecone_index_name"],
                "status": "planned",
            },
        }

        return provider_info.get(provider.lower(), {})

    @staticmethod
    def list_providers() -> Dict[str, Dict[str, Any]]:
        """List all available and planned providers."""
        providers = {}
        for provider in ["chromadb", "weaviate", "pinecone"]:
            providers[provider] = VectorStoreFactory.get_provider_info(provider)
        return providers
