"""
Ingestion Module

This module handles repository ingestion, file processing, and metadata extraction
for the LLM RAG Codebase Query System.
"""

from .exceptions import (
    IngestionError,
    AuthenticationError,
    RepositoryError,
    FileProcessingError,
    MetadataExtractionError,
    RateLimitError,
    ConfigurationError,
)

from .base import (
    RepositorySource,
    FileFilter,
    MetadataExtractor,
    IngestionPipeline,
    FileMetadata,
    RepositoryInfo,
    # Chunking components
    Chunk,
    ChunkType,
    ChunkContext,
    FileLoader,
    ChunkingStrategy,
    ChunkingPipeline,
    # Embedding components
    EmbeddingMetadata,
    EmbeddedChunk,
    SearchResult,
    EmbeddingClient,
    VectorStore,
    EmbeddingPipeline,
)

from .github import (
    GitHubClient,
    GitHubRepositoryManager,
    GitHubFileFilter,
    GitHubMetadataExtractor,
    GitHubConnector,
)

from .loader import UniversalFileLoader
from .pipeline import DefaultChunkingPipeline
from .strategies import (
    BaseChunkingStrategy,
    ChunkingStrategyFactory,
    TextChunkingStrategy,
    MarkdownChunkingStrategy,
    CodeChunkingStrategy,
    ConfigChunkingStrategy,
)

from .embedding_pipeline import DefaultEmbeddingPipeline
from .embedding import (
    OpenAIEmbeddingClient,
    LocalEmbeddingClient,
    EmbeddingClientFactory,
)
from .vector import (
    ChromaVectorStore,
    VectorStoreFactory,
)

__all__ = [
    # Exceptions
    "IngestionError",
    "AuthenticationError",
    "RepositoryError",
    "FileProcessingError",
    "MetadataExtractionError",
    "RateLimitError",
    "ConfigurationError",
    # Base classes
    "RepositorySource",
    "FileFilter",
    "MetadataExtractor",
    "IngestionPipeline",
    "FileMetadata",
    "RepositoryInfo",
    # Chunking components
    "Chunk",
    "ChunkType",
    "ChunkContext",
    "FileLoader",
    "ChunkingStrategy",
    "ChunkingPipeline",
    # Embedding components
    "EmbeddingMetadata",
    "EmbeddedChunk",
    "SearchResult",
    "EmbeddingClient",
    "VectorStore",
    "EmbeddingPipeline",
    # GitHub implementations
    "GitHubClient",
    "GitHubRepositoryManager",
    "GitHubFileFilter",
    "GitHubMetadataExtractor",
    "GitHubConnector",
    # Chunking implementations
    "UniversalFileLoader",
    "DefaultChunkingPipeline",
    "BaseChunkingStrategy",
    "ChunkingStrategyFactory",
    "TextChunkingStrategy",
    "MarkdownChunkingStrategy",
    "CodeChunkingStrategy",
    "ConfigChunkingStrategy",
    # Embedding implementations
    "DefaultEmbeddingPipeline",
    "OpenAIEmbeddingClient",
    "LocalEmbeddingClient",
    "EmbeddingClientFactory",
    "ChromaVectorStore",
    "VectorStoreFactory",
]
