"""
Local Embedding Client

This module provides local embedding generation using sentence-transformers
for privacy-sensitive deployments or offline usage.
"""

import logging
from typing import List, Dict, Any, Optional
import numpy as np

from ..base import EmbeddingClient
from ..exceptions import IngestionError
from ...config import Settings

logger = logging.getLogger(__name__)


class LocalEmbeddingClient(EmbeddingClient):
    """Local embedding client using sentence-transformers."""

    # Model configurations for local models
    MODEL_CONFIGS = {
        "all-MiniLM-L6-v2": {
            "dimension": 384,
            "max_tokens": 256,
            "model_size": "22MB",
        },
        "all-mpnet-base-v2": {
            "dimension": 768,
            "max_tokens": 384,
            "model_size": "420MB",
        },
        "multi-qa-MiniLM-L6-cos-v1": {
            "dimension": 384,
            "max_tokens": 512,
            "model_size": "22MB",
        },
    }

    def __init__(self, settings: Settings):
        """Initialize local embedding client."""
        self.settings = settings
        self.model_name = getattr(settings, "local_embedding_model", "all-MiniLM-L6-v2")
        self.batch_size = settings.embedding_batch_size
        self.normalize_embeddings = settings.embedding_normalize

        # Validate model
        if self.model_name not in self.MODEL_CONFIGS:
            logger.warning(f"Unknown local model {self.model_name}, using default configuration")
            self.model_config = {
                "dimension": 384,
                "max_tokens": 256,
                "model_size": "Unknown",
            }
        else:
            self.model_config = self.MODEL_CONFIGS[self.model_name]

        # Lazy load the model
        self._model = None
        self._device = None

        # Statistics
        self._stats = {
            "total_requests": 0,
            "total_texts": 0,
            "failed_requests": 0,
            "model_loaded": False,
        }

        logger.info(f"Initialized local embedding client with model {self.model_name}")

    def _load_model(self):
        """Lazy load the sentence transformer model."""
        if self._model is not None:
            return

        try:
            from sentence_transformers import SentenceTransformer
            import torch

            # Determine device
            if torch.cuda.is_available():
                self._device = "cuda"
                logger.info("Using CUDA for local embeddings")
            else:
                self._device = "cpu"
                logger.info("Using CPU for local embeddings")

            # Load model
            logger.info(f"Loading local embedding model: {self.model_name}")
            self._model = SentenceTransformer(self.model_name, device=self._device)
            self._stats["model_loaded"] = True

            logger.info(f"Successfully loaded model {self.model_name} on {self._device}")

        except ImportError as e:
            raise IngestionError(
                "sentence-transformers library is required for local embeddings. "
                "Install with: pip install sentence-transformers",
                details={"operation": "load_model"},
                cause=e,
            )
        except Exception as e:
            raise IngestionError(
                f"Failed to load local embedding model {self.model_name}: {e}",
                details={"operation": "load_model", "model_name": self.model_name},
                cause=e,
            )

    async def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings for a list of texts."""
        if not texts:
            return []

        self._load_model()

        try:
            # Process in batches
            all_embeddings = []

            for i in range(0, len(texts), self.batch_size):
                batch = texts[i : i + self.batch_size]

                # Generate embeddings
                batch_embeddings = self._model.encode(
                    batch,
                    convert_to_numpy=True,
                    normalize_embeddings=self.normalize_embeddings,
                    show_progress_bar=False,
                )

                # Convert to list of lists
                embeddings_list = [embedding.tolist() for embedding in batch_embeddings]
                all_embeddings.extend(embeddings_list)

            # Update statistics
            self._stats["total_requests"] += 1
            self._stats["total_texts"] += len(texts)

            logger.debug(f"Generated {len(all_embeddings)} local embeddings for {len(texts)} texts")
            return all_embeddings

        except Exception as e:
            self._stats["failed_requests"] += 1
            logger.error(f"Failed to generate local embeddings: {e}")
            raise IngestionError(
                f"Failed to generate local embeddings: {e}", details={"operation": "generate_embeddings"}, cause=e
            )

    async def generate_single_embedding(self, text: str) -> List[float]:
        """Generate embedding for a single text."""
        embeddings = await self.generate_embeddings([text])
        return embeddings[0] if embeddings else []

    def get_embedding_dimension(self) -> int:
        """Get the dimension of embeddings produced by this client."""
        return self.model_config["dimension"]

    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the embedding model."""
        return {
            "provider": "local",
            "model": self.model_name,
            "dimension": self.model_config["dimension"],
            "max_tokens": self.model_config["max_tokens"],
            "model_size": self.model_config["model_size"],
            "device": self._device,
            "normalize_embeddings": self.normalize_embeddings,
        }

    def estimate_cost(self, texts: List[str]) -> float:
        """Estimate the cost of generating embeddings (free for local)."""
        return 0.0  # Local embeddings are free

    def get_stats(self) -> Dict[str, Any]:
        """Get usage statistics."""
        return self._stats.copy()

    def reset_stats(self) -> None:
        """Reset usage statistics."""
        self._stats = {
            "total_requests": 0,
            "total_texts": 0,
            "failed_requests": 0,
            "model_loaded": self._stats["model_loaded"],  # Keep model loaded status
        }

    def unload_model(self) -> None:
        """Unload the model to free memory."""
        if self._model is not None:
            del self._model
            self._model = None
            self._stats["model_loaded"] = False
            logger.info("Unloaded local embedding model")

    def __del__(self):
        """Cleanup when object is destroyed."""
        try:
            self.unload_model()
        except:
            pass  # Ignore errors during cleanup
