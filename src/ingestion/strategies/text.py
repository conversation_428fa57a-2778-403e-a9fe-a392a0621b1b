"""
Text Chunking Strategy

This module provides chunking strategy for plain text files with semantic
paragraph boundaries and configurable overlap.
"""

import logging
import re
from typing import List

from ..base import Chun<PERSON>, ChunkType, ChunkContext, FileMetadata
from .base import BaseChunkingStrategy

logger = logging.getLogger(__name__)


class TextChunkingStrategy(BaseChunkingStrategy):
    """Chunking strategy for plain text files."""

    def get_strategy_name(self) -> str:
        """Get the name of this chunking strategy."""
        return "text"

    def is_applicable(self, file_metadata: FileMetadata) -> bool:
        """Check if this strategy is applicable to the file."""
        file_extension = file_metadata.file_path.split(".")[-1].lower() if "." in file_metadata.file_path else ""

        # Don't apply to code files (they have their own strategy)
        if file_metadata.language:
            return False

        # Don't apply to markdown files
        if file_extension in {"md", "markdown", "rst"}:
            return False

        # Don't apply to config files
        if file_extension in {"json", "yaml", "yml", "toml", "ini", "cfg", "conf", "env", "properties"}:
            return False

        # Apply to plain text files
        text_extensions = {"txt", "log", "readme", "license", "changelog", "contributing"}
        return (
            file_extension in text_extensions
            or file_metadata.file_type == "text"
            or (not file_extension and file_metadata.file_path.lower() in text_extensions)
        )

    async def chunk_content(self, content: str, file_metadata: FileMetadata, **kwargs) -> List[Chunk]:
        """Chunk text content into semantic paragraphs."""
        try:
            if not content.strip():
                return []

            # Split content into paragraphs
            paragraphs = self._split_into_paragraphs(content)

            # Group paragraphs into chunks based on token limits
            chunks = self._group_paragraphs_into_chunks(paragraphs)

            # Add overlap between chunks
            if len(chunks) > 1:
                chunks = self.add_overlap(chunks)

            # Create chunk objects
            result_chunks = []
            lines = content.split("\n")
            current_line = 1

            for i, chunk_content in enumerate(chunks):
                # Find line numbers for this chunk
                start_line = current_line
                chunk_lines = chunk_content.count("\n")
                end_line = start_line + chunk_lines

                # Create chunk
                chunk = self.create_chunk(
                    content=chunk_content,
                    file_metadata=file_metadata,
                    start_line=start_line,
                    end_line=end_line,
                    chunk_type=ChunkType.TEXT_PARAGRAPH,
                    chunk_index=i,
                    context=ChunkContext(),
                )

                result_chunks.append(chunk)
                current_line = end_line + 1

            logger.debug(f"Created {len(result_chunks)} text chunks for {file_metadata.file_path}")
            return result_chunks

        except Exception as e:
            logger.error(f"Failed to chunk text content for {file_metadata.file_path}: {e}")
            raise

    def _split_into_paragraphs(self, content: str) -> List[str]:
        """Split content into paragraphs."""
        # Split on double newlines (paragraph breaks)
        paragraphs = re.split(r"\n\s*\n", content)

        # Clean up paragraphs
        cleaned_paragraphs = []
        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if paragraph:
                cleaned_paragraphs.append(paragraph)

        return cleaned_paragraphs

    def _group_paragraphs_into_chunks(self, paragraphs: List[str]) -> List[str]:
        """Group paragraphs into chunks based on token limits."""
        if not paragraphs:
            return []

        chunks = []
        current_chunk_paragraphs = []
        current_tokens = 0

        for paragraph in paragraphs:
            paragraph_tokens = self.count_tokens(paragraph)

            # If single paragraph exceeds max tokens, split it
            if paragraph_tokens > self.max_chunk_size:
                # Save current chunk if it has content
                if current_chunk_paragraphs:
                    chunks.append("\n\n".join(current_chunk_paragraphs))
                    current_chunk_paragraphs = []
                    current_tokens = 0

                # Split large paragraph
                split_paragraphs = self._split_large_paragraph(paragraph)
                chunks.extend(split_paragraphs)

            elif current_tokens + paragraph_tokens > self.chunk_size:
                # Current chunk is full, start new one
                if current_chunk_paragraphs:
                    chunks.append("\n\n".join(current_chunk_paragraphs))

                current_chunk_paragraphs = [paragraph]
                current_tokens = paragraph_tokens

            else:
                # Add paragraph to current chunk
                current_chunk_paragraphs.append(paragraph)
                current_tokens += paragraph_tokens

        # Add remaining paragraphs as final chunk
        if current_chunk_paragraphs:
            chunks.append("\n\n".join(current_chunk_paragraphs))

        return chunks

    def _split_large_paragraph(self, paragraph: str) -> List[str]:
        """Split a large paragraph into smaller chunks."""
        # First try to split by sentences
        sentences = self._split_into_sentences(paragraph)

        if len(sentences) <= 1:
            # If no sentence breaks, split by token count
            return self.split_by_tokens(paragraph, self.chunk_size)

        # Group sentences into chunks
        chunks = []
        current_chunk_sentences = []
        current_tokens = 0

        for sentence in sentences:
            sentence_tokens = self.count_tokens(sentence)

            if sentence_tokens > self.chunk_size:
                # Save current chunk
                if current_chunk_sentences:
                    chunks.append(" ".join(current_chunk_sentences))
                    current_chunk_sentences = []
                    current_tokens = 0

                # Split long sentence by words
                word_chunks = self.split_by_tokens(sentence, self.chunk_size)
                chunks.extend(word_chunks)

            elif current_tokens + sentence_tokens > self.chunk_size:
                # Current chunk is full
                if current_chunk_sentences:
                    chunks.append(" ".join(current_chunk_sentences))

                current_chunk_sentences = [sentence]
                current_tokens = sentence_tokens

            else:
                current_chunk_sentences.append(sentence)
                current_tokens += sentence_tokens

        # Add remaining sentences
        if current_chunk_sentences:
            chunks.append(" ".join(current_chunk_sentences))

        return chunks

    def estimate_chunks(self, content: str) -> int:
        """Estimate number of chunks for text content."""
        paragraphs = self._split_into_paragraphs(content)

        if not paragraphs:
            return 0

        total_tokens = sum(self.count_tokens(p) for p in paragraphs)
        return max(1, (total_tokens + self.chunk_size - 1) // self.chunk_size)
