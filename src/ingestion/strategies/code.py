"""
Code Chunking Strategy

This module provides chunking strategy for code files with function/class-based
chunking and context preservation. Currently implements regex-based parsing
with plans for AST-based parsing in the future.
"""

import logging
import re
from typing import List, Dict, Tuple, Optional

from ..base import Chunk, ChunkType, ChunkContext, FileMetadata
from .base import BaseChunkingStrategy

logger = logging.getLogger(__name__)


class CodeChunkingStrategy(BaseChunkingStrategy):
    """Chunking strategy for code files with function/class boundaries."""

    # Language-specific patterns for function/class detection
    LANGUAGE_PATTERNS = {
        "python": {
            "class": r"^class\s+(\w+).*?:",
            "function": r"^(?:async\s+)?def\s+(\w+)\s*\(",
            "method": r"^\s+(?:async\s+)?def\s+(\w+)\s*\(",
            "import": r"^(?:from\s+\S+\s+)?import\s+",
            "comment": r"^\s*#",
            "docstring": r'^\s*""".*?"""',
        },
        "javascript": {
            "class": r"^class\s+(\w+)",
            "function": r"^(?:async\s+)?function\s+(\w+)\s*\(",
            "arrow_function": r"^(?:const|let|var)\s+(\w+)\s*=\s*(?:async\s*)?\(",
            "method": r"^\s*(?:async\s+)?(\w+)\s*\(",
            "import": r"^import\s+",
            "comment": r"^\s*//",
        },
        "typescript": {
            "class": r"^(?:export\s+)?class\s+(\w+)",
            "interface": r"^(?:export\s+)?interface\s+(\w+)",
            "function": r"^(?:export\s+)?(?:async\s+)?function\s+(\w+)\s*\(",
            "arrow_function": r"^(?:export\s+)?(?:const|let|var)\s+(\w+)\s*=\s*(?:async\s*)?\(",
            "method": r"^\s*(?:async\s+)?(\w+)\s*\(",
            "import": r"^import\s+",
            "comment": r"^\s*//",
        },
        "java": {
            "class": r"^(?:public\s+)?class\s+(\w+)",
            "interface": r"^(?:public\s+)?interface\s+(\w+)",
            "method": r"^\s*(?:public|private|protected)?\s*(?:static\s+)?(?:\w+\s+)*(\w+)\s*\(",
            "import": r"^import\s+",
            "comment": r"^\s*//",
        },
        "cpp": {
            "class": r"^class\s+(\w+)",
            "function": r"^(?:\w+\s+)*(\w+)\s*\(",
            "method": r"^\s*(?:\w+\s+)*(\w+)\s*\(",
            "include": r"^#include\s+",
            "comment": r"^\s*//",
        },
        "go": {
            "struct": r"^type\s+(\w+)\s+struct",
            "interface": r"^type\s+(\w+)\s+interface",
            "function": r"^func\s+(\w+)\s*\(",
            "method": r"^func\s+\(\w+\s+\*?\w+\)\s+(\w+)\s*\(",
            "import": r"^import\s+",
            "comment": r"^\s*//",
        },
    }

    def get_strategy_name(self) -> str:
        """Get the name of this chunking strategy."""
        return "code"

    def is_applicable(self, file_metadata: FileMetadata) -> bool:
        """Check if this strategy is applicable to the file."""
        if file_metadata.language:
            return file_metadata.language.lower() in self.LANGUAGE_PATTERNS

        file_extension = file_metadata.file_path.split(".")[-1].lower() if "." in file_metadata.file_path else ""
        code_extensions = {
            "py",
            "js",
            "ts",
            "jsx",
            "tsx",
            "java",
            "cpp",
            "c",
            "h",
            "hpp",
            "cs",
            "go",
            "rs",
            "php",
            "rb",
            "swift",
            "kt",
            "scala",
            "r",
            "sql",
        }
        return file_extension in code_extensions

    async def chunk_content(self, content: str, file_metadata: FileMetadata, **kwargs) -> List[Chunk]:
        """Chunk code content based on function/class boundaries."""
        try:
            if not content.strip():
                return []

            # Determine language
            language = self._detect_language(file_metadata)

            if language in self.LANGUAGE_PATTERNS:
                # Use language-specific parsing
                code_blocks = self._parse_code_structure(content, language)
            else:
                # Fallback to generic chunking
                code_blocks = self._generic_code_chunking(content)

            # Create chunks from code blocks
            chunks = []
            for i, block in enumerate(code_blocks):
                chunk = self._create_code_chunk(block, file_metadata, i)
                chunks.append(chunk)

            logger.debug(f"Created {len(chunks)} code chunks for {file_metadata.file_path}")
            return chunks

        except Exception as e:
            logger.error(f"Failed to chunk code content for {file_metadata.file_path}: {e}")
            raise

    def _detect_language(self, file_metadata: FileMetadata) -> str:
        """Detect programming language from file metadata."""
        if file_metadata.language:
            lang = file_metadata.language.lower()
            if lang in self.LANGUAGE_PATTERNS:
                return lang

        # Fallback to file extension
        file_extension = file_metadata.file_path.split(".")[-1].lower() if "." in file_metadata.file_path else ""

        extension_map = {
            "py": "python",
            "js": "javascript",
            "ts": "typescript",
            "jsx": "javascript",
            "tsx": "typescript",
            "java": "java",
            "cpp": "cpp",
            "c": "cpp",
            "h": "cpp",
            "hpp": "cpp",
            "go": "go",
        }

        return extension_map.get(file_extension, "generic")

    def _parse_code_structure(self, content: str, language: str) -> List[Dict]:
        """Parse code structure using language-specific patterns."""
        lines = content.split("\n")
        patterns = self.LANGUAGE_PATTERNS[language]

        blocks = []
        current_block = None
        current_class = None
        indent_stack = []

        for line_num, line in enumerate(lines, 1):
            stripped_line = line.strip()

            # Skip empty lines and comments for structure detection
            if not stripped_line or self._is_comment(line, patterns):
                if current_block:
                    current_block["lines"].append((line_num, line))
                continue

            # Detect indentation level
            indent_level = len(line) - len(line.lstrip())

            # Check for class definition
            class_match = self._match_pattern(stripped_line, patterns.get("class"))
            if class_match:
                # Save previous block
                if current_block:
                    current_block["end_line"] = line_num - 1
                    blocks.append(current_block)

                current_class = class_match.group(1)
                current_block = {
                    "type": "class",
                    "name": current_class,
                    "start_line": line_num,
                    "end_line": line_num,
                    "lines": [(line_num, line)],
                    "indent_level": indent_level,
                    "parent_class": None,
                    "functions": [],
                }
                continue

            # Check for function/method definition
            function_patterns = ["function", "method", "arrow_function"]
            function_match = None
            for pattern_name in function_patterns:
                if pattern_name in patterns:
                    function_match = self._match_pattern(stripped_line, patterns[pattern_name])
                    if function_match:
                        break

            if function_match:
                # Save previous block if it's not a class containing this function
                if current_block and (
                    current_block["type"] != "class" or indent_level <= current_block["indent_level"]
                ):
                    current_block["end_line"] = line_num - 1
                    blocks.append(current_block)
                    current_block = None

                function_name = function_match.group(1)
                current_block = {
                    "type": "function",
                    "name": function_name,
                    "start_line": line_num,
                    "end_line": line_num,
                    "lines": [(line_num, line)],
                    "indent_level": indent_level,
                    "parent_class": current_class if current_class and indent_level > 0 else None,
                }
                continue

            # Add line to current block
            if current_block:
                current_block["lines"].append((line_num, line))
            else:
                # Start a new generic block
                current_block = {
                    "type": "generic",
                    "name": "code_block",
                    "start_line": line_num,
                    "end_line": line_num,
                    "lines": [(line_num, line)],
                    "indent_level": indent_level,
                    "parent_class": None,
                }

        # Save final block
        if current_block:
            current_block["end_line"] = len(lines)
            blocks.append(current_block)

        return blocks

    def _match_pattern(self, line: str, pattern: Optional[str]) -> Optional[re.Match]:
        """Match a line against a regex pattern."""
        if not pattern:
            return None
        try:
            return re.match(pattern, line)
        except re.error:
            return None

    def _is_comment(self, line: str, patterns: Dict[str, str]) -> bool:
        """Check if line is a comment."""
        comment_pattern = patterns.get("comment")
        if comment_pattern:
            return bool(re.match(comment_pattern, line))
        return False

    def _generic_code_chunking(self, content: str) -> List[Dict]:
        """Generic code chunking when language-specific parsing is not available."""
        # Split by logical blocks (functions, classes, etc.) using indentation
        lines = content.split("\n")
        blocks = []
        current_block = []
        current_indent = 0

        for line_num, line in enumerate(lines, 1):
            if not line.strip():
                if current_block:
                    current_block.append((line_num, line))
                continue

            indent_level = len(line) - len(line.lstrip())

            # If indentation decreases significantly, start new block
            if current_block and indent_level < current_indent - 4:
                blocks.append(
                    {
                        "type": "generic",
                        "name": f"block_{len(blocks)}",
                        "start_line": current_block[0][0],
                        "end_line": current_block[-1][0],
                        "lines": current_block,
                        "indent_level": current_indent,
                        "parent_class": None,
                    }
                )
                current_block = []

            current_block.append((line_num, line))
            current_indent = indent_level

        # Add final block
        if current_block:
            blocks.append(
                {
                    "type": "generic",
                    "name": f"block_{len(blocks)}",
                    "start_line": current_block[0][0],
                    "end_line": current_block[-1][0],
                    "lines": current_block,
                    "indent_level": current_indent,
                    "parent_class": None,
                }
            )

        return blocks

    def _create_code_chunk(self, block: Dict, file_metadata: FileMetadata, block_index: int) -> Chunk:
        """Create a chunk from a code block."""
        # Reconstruct content from lines
        content = "\n".join(line for _, line in block["lines"])

        # Determine chunk type
        if block["type"] == "class":
            chunk_type = ChunkType.CODE_CLASS
        elif block["type"] == "function":
            chunk_type = ChunkType.CODE_FUNCTION
        else:
            chunk_type = ChunkType.CODE_MODULE

        # Create context
        context = ChunkContext(
            function_name=block["name"] if block["type"] == "function" else None,
            class_name=(
                block["parent_class"]
                if block["parent_class"]
                else (block["name"] if block["type"] == "class" else None)
            ),
            module_name=file_metadata.file_path.split("/")[-1].split(".")[0],
        )

        return self.create_chunk(
            content=content.strip(),
            file_metadata=file_metadata,
            start_line=block["start_line"],
            end_line=block["end_line"],
            chunk_type=chunk_type,
            chunk_index=block_index,
            context=context,
        )

    def estimate_chunks(self, content: str) -> int:
        """Estimate number of chunks for code content."""
        # Count potential function/class definitions
        function_patterns = [
            r"^\s*(?:def|function|class)\s+",  # Python, JS, general
            r"^\s*(?:public|private|protected).*\s+\w+\s*\(",  # Java methods
            r"^\s*func\s+",  # Go functions
        ]

        lines = content.split("\n")
        potential_chunks = 0

        for line in lines:
            for pattern in function_patterns:
                if re.match(pattern, line):
                    potential_chunks += 1
                    break

        # If no functions found, estimate based on token count
        if potential_chunks == 0:
            total_tokens = self.count_tokens(content)
            potential_chunks = max(1, (total_tokens + self.chunk_size - 1) // self.chunk_size)

        return potential_chunks
