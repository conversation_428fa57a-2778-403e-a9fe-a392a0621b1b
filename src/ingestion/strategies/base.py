"""
Base Chunking Strategy

This module provides the base chunking strategy class and factory for
automatic strategy selection based on file type and content.
"""

import logging
import uuid
from typing import List, Dict, Any, Optional, Type
from abc import ABC, abstractmethod
import tiktoken

from ..base import ChunkingStrategy, Chunk, ChunkType, ChunkContext, FileMetadata
from ..exceptions import FileProcessingError
from ...config import Settings

logger = logging.getLogger(__name__)


class BaseChunkingStrategy(ChunkingStrategy):
    """Base implementation of chunking strategy with common functionality."""

    def __init__(self, settings: Settings):
        """Initialize base chunking strategy."""
        self.settings = settings
        self.chunk_size = getattr(settings, "chunk_size", 1000)
        self.chunk_overlap = getattr(settings, "chunk_overlap", 200)
        self.max_chunk_size = getattr(settings, "max_chunk_size", 2000)
        self.min_chunk_size = getattr(settings, "min_chunk_size", 100)

        # Initialize tokenizer for token counting
        try:
            self.tokenizer = tiktoken.get_encoding("cl100k_base")  # GPT-4 tokenizer
        except Exception as e:
            logger.warning(f"Failed to initialize tokenizer: {e}, using character-based counting")
            self.tokenizer = None

    def count_tokens(self, text: str) -> int:
        """Count tokens in text."""
        if self.tokenizer:
            try:
                return len(self.tokenizer.encode(text))
            except Exception as e:
                logger.debug(f"Token counting failed: {e}, falling back to character count")

        # Fallback: approximate token count (1 token ≈ 4 characters)
        return len(text) // 4

    def generate_chunk_id(self, file_metadata: FileMetadata, chunk_index: int) -> str:
        """Generate unique chunk ID."""
        file_hash = hash(file_metadata.file_path)
        return f"{abs(file_hash):x}_{chunk_index}_{uuid.uuid4().hex[:8]}"

    def create_chunk(
        self,
        content: str,
        file_metadata: FileMetadata,
        start_line: int,
        end_line: int,
        chunk_type: ChunkType,
        chunk_index: int,
        context: Optional[ChunkContext] = None,
        start_char: Optional[int] = None,
        end_char: Optional[int] = None,
    ) -> Chunk:
        """Create a chunk with metadata."""
        chunk_id = self.generate_chunk_id(file_metadata, chunk_index)
        token_count = self.count_tokens(content)

        return Chunk(
            content=content.strip(),
            chunk_id=chunk_id,
            chunk_type=chunk_type,
            file_metadata=file_metadata,
            start_line=start_line,
            end_line=end_line,
            start_char=start_char,
            end_char=end_char,
            context=context or ChunkContext(),
            token_count=token_count,
            char_count=len(content),
            chunking_strategy=self.get_strategy_name(),
        )

    def split_by_lines(self, content: str, max_lines: int = 50) -> List[str]:
        """Split content by lines with maximum line count per chunk."""
        lines = content.split("\n")
        chunks = []

        for i in range(0, len(lines), max_lines):
            chunk_lines = lines[i : i + max_lines]
            chunk_content = "\n".join(chunk_lines)
            if chunk_content.strip():
                chunks.append(chunk_content)

        return chunks

    def split_by_tokens(self, content: str, max_tokens: int = None) -> List[str]:
        """Split content by token count."""
        if max_tokens is None:
            max_tokens = self.chunk_size

        # Simple implementation: split by sentences first, then by tokens
        sentences = self._split_into_sentences(content)
        chunks = []
        current_chunk = []
        current_tokens = 0

        for sentence in sentences:
            sentence_tokens = self.count_tokens(sentence)

            # If single sentence exceeds max tokens, split it further
            if sentence_tokens > max_tokens:
                if current_chunk:
                    chunks.append(" ".join(current_chunk))
                    current_chunk = []
                    current_tokens = 0

                # Split long sentence by words
                words = sentence.split()
                word_chunk = []
                word_tokens = 0

                for word in words:
                    word_token_count = self.count_tokens(word)
                    if word_tokens + word_token_count > max_tokens and word_chunk:
                        chunks.append(" ".join(word_chunk))
                        word_chunk = [word]
                        word_tokens = word_token_count
                    else:
                        word_chunk.append(word)
                        word_tokens += word_token_count

                if word_chunk:
                    chunks.append(" ".join(word_chunk))

            elif current_tokens + sentence_tokens > max_tokens:
                if current_chunk:
                    chunks.append(" ".join(current_chunk))
                current_chunk = [sentence]
                current_tokens = sentence_tokens
            else:
                current_chunk.append(sentence)
                current_tokens += sentence_tokens

        if current_chunk:
            chunks.append(" ".join(current_chunk))

        return [chunk for chunk in chunks if chunk.strip()]

    def _split_into_sentences(self, text: str) -> List[str]:
        """Split text into sentences."""
        # Simple sentence splitting - can be enhanced with NLTK or spaCy
        import re

        # Split on sentence endings, but preserve them
        sentences = re.split(r"([.!?]+)", text)

        # Recombine sentences with their punctuation
        result = []
        for i in range(0, len(sentences) - 1, 2):
            sentence = sentences[i]
            if i + 1 < len(sentences):
                sentence += sentences[i + 1]

            sentence = sentence.strip()
            if sentence:
                result.append(sentence)

        # Handle case where text doesn't end with sentence punctuation
        if len(sentences) % 2 == 1 and sentences[-1].strip():
            result.append(sentences[-1].strip())

        return result

    def add_overlap(self, chunks: List[str], overlap_size: int = None) -> List[str]:
        """Add overlap between consecutive chunks."""
        if len(chunks) <= 1:
            return chunks

        if overlap_size is None:
            overlap_size = self.chunk_overlap

        overlapped_chunks = []

        for i, chunk in enumerate(chunks):
            if i == 0:
                overlapped_chunks.append(chunk)
            else:
                # Get overlap from previous chunk
                prev_chunk = chunks[i - 1]
                prev_tokens = self.count_tokens(prev_chunk)

                if prev_tokens > overlap_size:
                    # Take last part of previous chunk as overlap
                    prev_words = prev_chunk.split()
                    overlap_words = []
                    overlap_tokens = 0

                    for word in reversed(prev_words):
                        word_tokens = self.count_tokens(word)
                        if overlap_tokens + word_tokens > overlap_size:
                            break
                        overlap_words.insert(0, word)
                        overlap_tokens += word_tokens

                    if overlap_words:
                        overlap_text = " ".join(overlap_words)
                        overlapped_chunk = overlap_text + " " + chunk
                    else:
                        overlapped_chunk = chunk
                else:
                    # Previous chunk is small, use it entirely as overlap
                    overlapped_chunk = prev_chunk + " " + chunk

                overlapped_chunks.append(overlapped_chunk)

        return overlapped_chunks

    def estimate_chunks(self, content: str) -> int:
        """Estimate number of chunks for the content."""
        total_tokens = self.count_tokens(content)
        return max(1, (total_tokens + self.chunk_size - 1) // self.chunk_size)


class ChunkingStrategyFactory:
    """Factory for creating appropriate chunking strategies."""

    def __init__(self, settings: Settings):
        """Initialize strategy factory."""
        self.settings = settings
        self._strategies: Dict[str, Type[ChunkingStrategy]] = {}
        self._register_default_strategies()

    def _register_default_strategies(self) -> None:
        """Register default chunking strategies."""
        # Import here to avoid circular imports
        from .text import TextChunkingStrategy
        from .markdown import MarkdownChunkingStrategy
        from .code import CodeChunkingStrategy
        from .config import ConfigChunkingStrategy

        self._strategies.update(
            {
                "text": TextChunkingStrategy,
                "markdown": MarkdownChunkingStrategy,
                "code": CodeChunkingStrategy,
                "config": ConfigChunkingStrategy,
            }
        )

    def register_strategy(self, name: str, strategy_class: Type[ChunkingStrategy]) -> None:
        """Register a custom chunking strategy."""
        self._strategies[name] = strategy_class
        logger.info(f"Registered chunking strategy: {name}")

    def get_strategy(self, file_metadata: FileMetadata) -> ChunkingStrategy:
        """Get appropriate chunking strategy for a file."""
        # Determine strategy based on file type and language
        file_extension = file_metadata.file_path.split(".")[-1].lower() if "." in file_metadata.file_path else ""
        language = file_metadata.language

        # Markdown files
        if file_extension in ["md", "markdown", "rst"]:
            return self._strategies["markdown"](self.settings)

        # Configuration files
        if file_extension in ["json", "yaml", "yml", "toml", "ini", "cfg", "conf", "env", "properties"]:
            return self._strategies["config"](self.settings)

        # Code files
        if language or file_extension in [
            "py",
            "js",
            "ts",
            "jsx",
            "tsx",
            "java",
            "cpp",
            "c",
            "h",
            "hpp",
            "cs",
            "go",
            "rs",
            "php",
            "rb",
            "swift",
            "kt",
            "scala",
            "r",
            "sql",
        ]:
            return self._strategies["code"](self.settings)

        # Default to text strategy
        return self._strategies["text"](self.settings)

    def get_available_strategies(self) -> List[str]:
        """Get list of available strategy names."""
        return list(self._strategies.keys())
