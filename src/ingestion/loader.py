"""
File Content Loader

This module provides robust file loading capabilities with automatic encoding
detection, content normalization, and support for various file types.
"""

import logging
import mimetypes
from typing import Optional, Dict, Any, Set
from pathlib import Path
import chardet

from .base import FileLoader
from .exceptions import FileProcessingError
from ..config import Settings

logger = logging.getLogger(__name__)


class UniversalFileLoader(FileLoader):
    """Universal file loader with encoding detection and content normalization."""

    # Supported text file extensions
    SUPPORTED_EXTENSIONS = {
        # Programming languages
        ".py",
        ".js",
        ".ts",
        ".jsx",
        ".tsx",
        ".java",
        ".cpp",
        ".c",
        ".h",
        ".hpp",
        ".cs",
        ".go",
        ".rs",
        ".php",
        ".rb",
        ".swift",
        ".kt",
        ".scala",
        ".r",
        ".sql",
        ".sh",
        ".bash",
        ".zsh",
        ".fish",
        ".ps1",
        ".bat",
        ".cmd",
        # Web technologies
        ".html",
        ".htm",
        ".css",
        ".scss",
        ".sass",
        ".less",
        ".xml",
        ".svg",
        # Data and configuration
        ".json",
        ".yaml",
        ".yml",
        ".toml",
        ".ini",
        ".cfg",
        ".conf",
        ".env",
        ".properties",
        ".plist",
        # Documentation
        ".md",
        ".rst",
        ".txt",
        ".log",
        ".dockerfile",
        ".makefile",
        ".cmake",
        ".gradle",
        ".lock",
        ".gitignore",
        ".gitattributes",
        # Special files (no extension)
        "dockerfile",
        "makefile",
        "rakefile",
        "gemfile",
        "readme",
        "license",
        "changelog",
        "contributing",
        "authors",
        "notice",
        "copying",
    }

    # Common encoding fallbacks
    ENCODING_FALLBACKS = ["utf-8", "utf-16", "latin-1", "cp1252", "ascii"]

    def __init__(self, settings: Settings):
        """Initialize file loader with configuration."""
        self.settings = settings
        self.max_file_size = settings.max_file_size

        # Cache for encoding detection results
        self._encoding_cache: Dict[str, str] = {}

    async def load_file(self, file_path: Path) -> str:
        """Load file content with proper encoding detection."""
        try:
            if not file_path.exists():
                raise FileProcessingError(
                    f"File does not exist: {file_path}", file_path=str(file_path), operation="load_file"
                )

            if not file_path.is_file():
                raise FileProcessingError(
                    f"Path is not a file: {file_path}", file_path=str(file_path), operation="load_file"
                )

            # Check file size
            file_size = file_path.stat().st_size
            if file_size > self.max_file_size:
                raise FileProcessingError(
                    f"File too large: {file_size} bytes (max: {self.max_file_size})",
                    file_path=str(file_path),
                    operation="load_file",
                    details={"file_size": file_size, "max_size": self.max_file_size},
                )

            # Check if file type is supported
            if not self.is_supported(file_path):
                raise FileProcessingError(
                    f"Unsupported file type: {file_path}", file_path=str(file_path), operation="load_file"
                )

            # Detect encoding
            encoding = self.detect_encoding(file_path)

            # Load file content
            try:
                with open(file_path, "r", encoding=encoding) as f:
                    content = f.read()

                # Normalize content
                content = self._normalize_content(content)

                logger.debug(f"Successfully loaded file {file_path} with encoding {encoding}")
                return content

            except UnicodeDecodeError as e:
                logger.warning(f"Failed to decode {file_path} with {encoding}, trying fallbacks")

                # Try fallback encodings
                for fallback_encoding in self.ENCODING_FALLBACKS:
                    if fallback_encoding == encoding:
                        continue

                    try:
                        with open(file_path, "r", encoding=fallback_encoding) as f:
                            content = f.read()

                        content = self._normalize_content(content)
                        logger.info(f"Successfully loaded {file_path} with fallback encoding {fallback_encoding}")

                        # Update cache with working encoding
                        self._encoding_cache[str(file_path)] = fallback_encoding
                        return content

                    except UnicodeDecodeError:
                        continue

                # If all encodings fail, try reading as binary and decode with errors='replace'
                try:
                    with open(file_path, "rb") as f:
                        raw_content = f.read()

                    content = raw_content.decode("utf-8", errors="replace")
                    content = self._normalize_content(content)

                    logger.warning(f"Loaded {file_path} with error replacement")
                    return content

                except Exception as binary_error:
                    raise FileProcessingError(
                        f"Failed to load file with any encoding: {e}",
                        file_path=str(file_path),
                        operation="load_file",
                        details={"original_error": str(e), "binary_error": str(binary_error)},
                        cause=e,
                    )

        except FileProcessingError:
            raise
        except Exception as e:
            logger.error(f"Unexpected error loading file {file_path}: {e}")
            raise FileProcessingError(
                f"Unexpected error loading file: {e}", file_path=str(file_path), operation="load_file", cause=e
            )

    def detect_encoding(self, file_path: Path) -> str:
        """Detect file encoding using chardet."""
        file_path_str = str(file_path)

        # Check cache first
        if file_path_str in self._encoding_cache:
            return self._encoding_cache[file_path_str]

        try:
            # Read a sample of the file for encoding detection
            sample_size = min(8192, file_path.stat().st_size)

            with open(file_path, "rb") as f:
                raw_data = f.read(sample_size)

            if not raw_data:
                # Empty file, default to utf-8
                encoding = "utf-8"
            else:
                # Use chardet to detect encoding
                detection_result = chardet.detect(raw_data)
                encoding = detection_result.get("encoding", "utf-8")
                confidence = detection_result.get("confidence", 0.0)

                # If confidence is low, fall back to utf-8
                if confidence < 0.7:
                    logger.debug(f"Low confidence ({confidence}) for encoding detection of {file_path}, using utf-8")
                    encoding = "utf-8"

                # Normalize encoding name
                if encoding:
                    encoding = encoding.lower()
                    # Handle common encoding aliases
                    if encoding in ["ascii", "us-ascii"]:
                        encoding = "utf-8"
                    elif encoding in ["iso-8859-1", "latin1"]:
                        encoding = "latin-1"
                else:
                    encoding = "utf-8"

            # Cache the result
            self._encoding_cache[file_path_str] = encoding

            logger.debug(f"Detected encoding for {file_path}: {encoding}")
            return encoding

        except Exception as e:
            logger.warning(f"Failed to detect encoding for {file_path}: {e}, defaulting to utf-8")
            encoding = "utf-8"
            self._encoding_cache[file_path_str] = encoding
            return encoding

    def is_supported(self, file_path: Path) -> bool:
        """Check if file type is supported."""
        # Check by extension
        extension = file_path.suffix.lower()
        if extension in self.SUPPORTED_EXTENSIONS:
            return True

        # Check by filename (for files without extensions)
        filename = file_path.name.lower()
        if filename in self.SUPPORTED_EXTENSIONS:
            return True

        # Check MIME type for additional validation
        mime_type, _ = mimetypes.guess_type(str(file_path))
        if mime_type and mime_type.startswith("text/"):
            return True

        # Special handling for common files without extensions
        if not extension and filename in {
            "dockerfile",
            "makefile",
            "rakefile",
            "gemfile",
            "readme",
            "license",
            "changelog",
            "contributing",
            "authors",
            "notice",
            "copying",
        }:
            return True

        return False

    def _normalize_content(self, content: str) -> str:
        """Normalize file content."""
        # Normalize line endings to \n
        content = content.replace("\r\n", "\n").replace("\r", "\n")

        # Remove BOM if present
        if content.startswith("\ufeff"):
            content = content[1:]

        # Ensure content ends with newline if it's not empty
        if content and not content.endswith("\n"):
            content += "\n"

        return content

    def get_file_info(self, file_path: Path) -> Dict[str, Any]:
        """Get detailed information about a file."""
        try:
            stat = file_path.stat()
            mime_type, _ = mimetypes.guess_type(str(file_path))

            return {
                "path": str(file_path),
                "size": stat.st_size,
                "extension": file_path.suffix.lower(),
                "mime_type": mime_type,
                "is_supported": self.is_supported(file_path),
                "detected_encoding": self.detect_encoding(file_path) if self.is_supported(file_path) else None,
            }
        except Exception as e:
            logger.warning(f"Failed to get file info for {file_path}: {e}")
            return {
                "path": str(file_path),
                "error": str(e),
            }

    def clear_encoding_cache(self) -> None:
        """Clear the encoding detection cache."""
        self._encoding_cache.clear()
        logger.debug("Cleared encoding detection cache")
