"""
Custom exceptions for the ingestion module.

This module defines all custom exceptions used throughout the ingestion pipeline,
following the centralized error handling pattern defined in docs/rules.md.
"""

from typing import Optional, Dict, Any


class IngestionError(Exception):
    """Base exception for all ingestion-related errors."""

    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None, cause: Optional[Exception] = None):
        super().__init__(message)
        self.message = message
        self.details = details or {}
        self.cause = cause

    def __str__(self) -> str:
        if self.details:
            return f"{self.message} - Details: {self.details}"
        return self.message


class AuthenticationError(IngestionError):
    """Raised when authentication with external services fails."""

    def __init__(
        self,
        message: str = "Authentication failed",
        service: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None,
    ):
        super().__init__(message, details, cause)
        self.service = service


class RepositoryError(IngestionError):
    """Raised when repository operations fail."""

    def __init__(
        self,
        message: str,
        repository_url: Optional[str] = None,
        operation: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None,
    ):
        super().__init__(message, details, cause)
        self.repository_url = repository_url
        self.operation = operation


class FileProcessingError(IngestionError):
    """Raised when file processing operations fail."""

    def __init__(
        self,
        message: str,
        file_path: Optional[str] = None,
        operation: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None,
    ):
        super().__init__(message, details, cause)
        self.file_path = file_path
        self.operation = operation


class MetadataExtractionError(IngestionError):
    """Raised when metadata extraction fails."""

    def __init__(
        self,
        message: str,
        source: Optional[str] = None,
        metadata_type: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None,
    ):
        super().__init__(message, details, cause)
        self.source = source
        self.metadata_type = metadata_type


class RateLimitError(IngestionError):
    """Raised when API rate limits are exceeded."""

    def __init__(
        self,
        message: str = "Rate limit exceeded",
        service: Optional[str] = None,
        retry_after: Optional[int] = None,
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None,
    ):
        super().__init__(message, details, cause)
        self.service = service
        self.retry_after = retry_after


class ConfigurationError(IngestionError):
    """Raised when configuration is invalid or missing."""

    def __init__(
        self,
        message: str,
        config_key: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None,
    ):
        super().__init__(message, details, cause)
        self.config_key = config_key


class EmbeddingError(IngestionError):
    """Base class for embedding-related errors."""

    def __init__(
        self,
        message: str,
        embedding_provider: Optional[str] = None,
        model_name: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None,
    ):
        super().__init__(message, details, cause)
        self.embedding_provider = embedding_provider
        self.model_name = model_name


class EmbeddingGenerationError(EmbeddingError):
    """Raised when embedding generation fails."""

    def __init__(self, message: str, text_content: Optional[str] = None, batch_size: Optional[int] = None, **kwargs):
        super().__init__(message, **kwargs)
        self.text_content = text_content
        self.batch_size = batch_size


class EmbeddingQualityError(EmbeddingError):
    """Raised when embedding quality validation fails."""

    def __init__(
        self,
        message: str,
        embedding_dimension: Optional[int] = None,
        expected_dimension: Optional[int] = None,
        **kwargs,
    ):
        super().__init__(message, **kwargs)
        self.embedding_dimension = embedding_dimension
        self.expected_dimension = expected_dimension


class VectorStoreError(IngestionError):
    """Base class for vector store-related errors."""

    def __init__(
        self,
        message: str,
        store_provider: Optional[str] = None,
        collection_name: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None,
    ):
        super().__init__(message, details, cause)
        self.store_provider = store_provider
        self.collection_name = collection_name


class VectorStoreConnectionError(VectorStoreError):
    """Raised when vector store connection fails."""

    def __init__(self, message: str, connection_url: Optional[str] = None, **kwargs):
        super().__init__(message, **kwargs)
        self.connection_url = connection_url


class VectorStoreOperationError(VectorStoreError):
    """Raised when vector store operations fail."""

    def __init__(
        self, message: str, operation_type: Optional[str] = None, embedding_count: Optional[int] = None, **kwargs
    ):
        super().__init__(message, **kwargs)
        self.operation_type = operation_type
        self.embedding_count = embedding_count


class EmbeddingPipelineError(IngestionError):
    """Raised when embedding pipeline operations fail."""

    def __init__(
        self,
        message: str,
        pipeline_stage: Optional[str] = None,
        chunk_count: Optional[int] = None,
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None,
    ):
        super().__init__(message, details, cause)
        self.pipeline_stage = pipeline_stage
        self.chunk_count = chunk_count
