"""
Chunking Pipeline

This module provides the main orchestrator for the chunking pipeline,
coordinating file loading, chunking strategies, and metadata preservation.
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path
from datetime import datetime

from .base import ChunkingPipeline, Chunk, FileMetadata
from .loader import UniversalFileLoader
from .strategies import ChunkingStrategyFactory
from .exceptions import FileProcessingError, IngestionError
from ..config import Settings

logger = logging.getLogger(__name__)


class DefaultChunkingPipeline(ChunkingPipeline):
    """Default implementation of the chunking pipeline."""

    def __init__(
        self,
        settings: Settings,
        file_loader: Optional[UniversalFileLoader] = None,
        strategy_factory: Optional[ChunkingStrategyFactory] = None,
    ):
        """Initialize chunking pipeline with dependencies."""
        self.settings = settings
        self.file_loader = file_loader or UniversalFileLoader(settings)
        self.strategy_factory = strategy_factory or ChunkingStrategyFactory(settings)

        # Processing statistics
        self._stats = {
            "files_processed": 0,
            "chunks_created": 0,
            "errors_encountered": 0,
            "processing_time": 0.0,
        }

        logger.info("Initialized chunking pipeline")

    async def process_files(self, file_metadata_list: List[FileMetadata], repository_path: Path) -> List[Chunk]:
        """Process multiple files and generate chunks."""
        start_time = datetime.now()

        try:
            logger.info(f"Processing {len(file_metadata_list)} files for chunking")

            all_chunks = []
            batch_size = getattr(self.settings, "chunk_batch_size", 20)

            # Process files in batches to manage memory
            for i in range(0, len(file_metadata_list), batch_size):
                batch = file_metadata_list[i : i + batch_size]
                logger.debug(f"Processing batch {i//batch_size + 1}: {len(batch)} files")

                # Process batch concurrently
                batch_tasks = [self.process_single_file(file_metadata, repository_path) for file_metadata in batch]

                batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)

                # Collect successful results and log errors
                for file_metadata, result in zip(batch, batch_results):
                    if isinstance(result, Exception):
                        logger.warning(f"Failed to process file {file_metadata.file_path}: {result}")
                        self._stats["errors_encountered"] += 1
                    elif result:
                        all_chunks.extend(result)
                        self._stats["chunks_created"] += len(result)

                    self._stats["files_processed"] += 1

            end_time = datetime.now()
            self._stats["processing_time"] = (end_time - start_time).total_seconds()

            logger.info(
                f"Chunking completed: {len(all_chunks)} chunks from "
                f"{self._stats['files_processed']} files in "
                f"{self._stats['processing_time']:.2f} seconds"
            )

            return all_chunks

        except Exception as e:
            logger.error(f"Failed to process files for chunking: {e}")
            raise IngestionError(
                f"Chunking pipeline failed: {e}",
                details={
                    "file_count": len(file_metadata_list),
                    "repository_path": str(repository_path),
                },
                cause=e,
            )

    async def process_single_file(self, file_metadata: FileMetadata, repository_path: Path) -> List[Chunk]:
        """Process a single file and generate chunks."""
        try:
            file_path = repository_path / file_metadata.file_path

            # Check if file exists and is supported
            if not file_path.exists():
                logger.warning(f"File not found: {file_path}")
                return []

            if not self.file_loader.is_supported(file_path):
                logger.debug(f"File type not supported for chunking: {file_path}")
                return []

            # Load file content
            try:
                content = await self.file_loader.load_file(file_path)
            except FileProcessingError as e:
                logger.warning(f"Failed to load file {file_path}: {e}")
                return []

            if not content.strip():
                logger.debug(f"Empty file, skipping: {file_path}")
                return []

            # Get appropriate chunking strategy
            strategy = self.get_chunking_strategy(file_metadata)

            # Generate chunks
            chunks = await strategy.chunk_content(content, file_metadata)

            # Post-process chunks
            chunks = self._post_process_chunks(chunks, file_metadata)

            logger.debug(f"Generated {len(chunks)} chunks for {file_metadata.file_path}")
            return chunks

        except Exception as e:
            logger.error(f"Error processing file {file_metadata.file_path}: {e}")
            raise FileProcessingError(
                f"Failed to process file for chunking: {e}",
                file_path=file_metadata.file_path,
                operation="chunk_file",
                cause=e,
            )

    def get_chunking_strategy(self, file_metadata: FileMetadata):
        """Get appropriate chunking strategy for a file."""
        return self.strategy_factory.get_strategy(file_metadata)

    def _post_process_chunks(self, chunks: List[Chunk], file_metadata: FileMetadata) -> List[Chunk]:
        """Post-process chunks to ensure quality and consistency."""
        processed_chunks = []

        for chunk in chunks:
            # Skip chunks that are too small or too large
            if chunk.token_count and chunk.token_count < getattr(self.settings, "min_chunk_size", 100):
                logger.debug(f"Skipping small chunk ({chunk.token_count} tokens) from {file_metadata.file_path}")
                continue

            if chunk.token_count and chunk.token_count > getattr(self.settings, "max_chunk_size", 2000):
                logger.warning(f"Large chunk ({chunk.token_count} tokens) from {file_metadata.file_path}")
                # Could split large chunks here if needed

            # Ensure chunk has valid content
            if not chunk.content.strip():
                continue

            # Inherit priority from file metadata
            if hasattr(file_metadata, "priority") and file_metadata.priority:
                # Chunks inherit file priority but can be adjusted based on content
                chunk.file_metadata.priority = file_metadata.priority

            processed_chunks.append(chunk)

        return processed_chunks

    def get_processing_stats(self) -> Dict[str, Any]:
        """Get processing statistics."""
        return self._stats.copy()

    def reset_stats(self) -> None:
        """Reset processing statistics."""
        self._stats = {
            "files_processed": 0,
            "chunks_created": 0,
            "errors_encountered": 0,
            "processing_time": 0.0,
        }

    async def estimate_processing_time(
        self, file_metadata_list: List[FileMetadata], repository_path: Path
    ) -> Dict[str, Any]:
        """Estimate processing time and chunk count."""
        total_files = len(file_metadata_list)
        total_chunks_estimate = 0
        supported_files = 0

        for file_metadata in file_metadata_list:
            file_path = repository_path / file_metadata.file_path

            if not file_path.exists() or not self.file_loader.is_supported(file_path):
                continue

            supported_files += 1

            try:
                # Quick content check for estimation
                file_size = file_path.stat().st_size
                if file_size > self.settings.max_file_size:
                    continue

                # Get strategy and estimate chunks
                strategy = self.get_chunking_strategy(file_metadata)

                # For estimation, we can use file size as a proxy
                estimated_tokens = file_size // 4  # Rough approximation
                chunk_size = getattr(self.settings, "chunk_size", 1000)
                file_chunks = max(1, estimated_tokens // chunk_size)

                total_chunks_estimate += file_chunks

            except Exception as e:
                logger.debug(f"Failed to estimate chunks for {file_metadata.file_path}: {e}")

        # Estimate processing time (rough approximation)
        # Assume ~100 files per second for chunking
        estimated_time = supported_files / 100.0

        return {
            "total_files": total_files,
            "supported_files": supported_files,
            "estimated_chunks": total_chunks_estimate,
            "estimated_time_seconds": estimated_time,
        }

    async def validate_chunks(self, chunks: List[Chunk]) -> Dict[str, Any]:
        """Validate generated chunks for quality and consistency."""
        validation_results = {
            "total_chunks": len(chunks),
            "valid_chunks": 0,
            "empty_chunks": 0,
            "oversized_chunks": 0,
            "undersized_chunks": 0,
            "missing_metadata": 0,
            "duplicate_ids": 0,
        }

        chunk_ids = set()
        min_size = getattr(self.settings, "min_chunk_size", 100)
        max_size = getattr(self.settings, "max_chunk_size", 2000)

        for chunk in chunks:
            # Check for empty content
            if not chunk.content.strip():
                validation_results["empty_chunks"] += 1
                continue

            # Check chunk size
            if chunk.token_count:
                if chunk.token_count < min_size:
                    validation_results["undersized_chunks"] += 1
                elif chunk.token_count > max_size:
                    validation_results["oversized_chunks"] += 1

            # Check for duplicate IDs
            if chunk.chunk_id in chunk_ids:
                validation_results["duplicate_ids"] += 1
            else:
                chunk_ids.add(chunk.chunk_id)

            # Check metadata completeness
            if not chunk.file_metadata or not chunk.file_metadata.file_path:
                validation_results["missing_metadata"] += 1

            validation_results["valid_chunks"] += 1

        return validation_results
