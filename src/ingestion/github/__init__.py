"""
GitHub Integration Module

This module provides GitHub-specific implementations for repository ingestion,
including API client, repository management, and metadata extraction.
"""

from .client import GitHubClient
from .repository import GitHubRepositoryManager
from .filters import GitHubFileFilter
from .metadata import GitHubMetadataExtractor
from .connector import GitHubConnector

__all__ = [
    "GitHubClient",
    "GitHubRepositoryManager",
    "GitHubFileFilter",
    "GitHubMetadataExtractor",
    "GitHubConnector",
]
