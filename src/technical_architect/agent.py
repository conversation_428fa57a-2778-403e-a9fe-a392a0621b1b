"""
Technical Design Architect Agent

This module implements the main Technical Architect agent that coordinates analysis,
design generation, validation, and standards compliance checking.
"""

import logging
import time
from typing import Any

from ..agents.base import Agent, AgentContext, AgentResponse, AgentType
from ..agents.exceptions import AgentError
from ..agents.formatters import MarkdownFormatter
from ..agents.llm_client import LLMClient
from ..config import Settings
from ..ingestion.integration_example import IntegratedIngestionPipeline
from .analyzer import ArchitectureAnalyzer
from .designer import DesignType, TechnicalDesigner
from .standards import ProjectStandardsManager
from .validator import SOLIDValidator

logger = logging.getLogger(__name__)


class TechnicalArchitectAgent(Agent):
    """Agent for technical design and architecture analysis."""

    def __init__(
        self,
        settings: Settings,
        llm_client: LLMClient,
        formatter: MarkdownFormatter,
        ingestion_pipeline: IntegratedIngestionPipeline,
    ):
        super().__init__(AgentType.TECHNICAL_ARCHITECT, settings)
        self.llm_client = llm_client
        self.formatter = formatter
        self.ingestion_pipeline = ingestion_pipeline

        # Initialize components
        self.analyzer = ArchitectureAnalyzer()
        self.designer = TechnicalDesigner()
        self.validator = SOLIDValidator()
        self.standards_manager = ProjectStandardsManager()

        # Configuration
        self.max_search_results = 20
        self.min_confidence_threshold = 0.7

        logger.info("Initialized Technical Design Architect Agent")

    def can_handle_query(self, query: str, context: AgentContext) -> float:
        """Return confidence score for handling this query."""
        query_lower = query.lower()

        # High confidence keywords
        high_confidence_keywords = [
            "architecture",
            "design",
            "pattern",
            "solid",
            "refactor",
            "structure",
            "component",
            "system design",
            "technical design",
        ]

        # Medium confidence keywords
        medium_confidence_keywords = [
            "implement",
            "create",
            "build",
            "develop",
            "standards",
            "best practices",
            "principles",
            "guidelines",
        ]

        # Low confidence keywords (should go to other agents)
        low_confidence_keywords = [
            "task",
            "plan",
            "timeline",
            "schedule",
            "what is",
            "find",
            "search",
            "lookup",
        ]

        # Calculate confidence based on keyword presence
        if any(keyword in query_lower for keyword in high_confidence_keywords):
            return 0.9

        if any(keyword in query_lower for keyword in medium_confidence_keywords):
            # Check if it's more about planning (should go to Task Planner)
            if any(keyword in query_lower for keyword in ["plan", "timeline", "schedule", "task"]):
                return 0.3
            return 0.7

        if any(keyword in query_lower for keyword in low_confidence_keywords):
            return 0.2

        # Check for design-related questions
        if "how to" in query_lower and any(
            keyword in query_lower for keyword in ["design", "implement", "structure", "organize"]
        ):
            return 0.8

        # Default medium-low confidence
        return 0.4

    async def process_query(self, query: str, context: AgentContext) -> AgentResponse:
        """Process a technical architecture query."""
        start_time = time.time()

        try:
            logger.info(f"Processing technical architecture query: {query[:100]}...")

            # Determine query type and approach
            query_type = self._classify_query_type(query)

            # Perform search for relevant code and documentation
            search_results = await self._perform_search(query, context)

            # Analyze current architecture
            architecture_analysis = self.analyzer.analyze_codebase(search_results)

            # Get project standards context
            standards_context = self.standards_manager.get_standards_context()

            # Generate response based on query type
            if query_type == "analysis":
                response_content = await self._generate_analysis_response(
                    query, architecture_analysis, search_results
                )
            elif query_type == "design":
                response_content = await self._generate_design_response(
                    query, architecture_analysis, standards_context
                )
            elif query_type == "validation":
                response_content = await self._generate_validation_response(
                    query, architecture_analysis, search_results
                )
            elif query_type == "standards":
                response_content = await self._generate_standards_response(
                    query, standards_context
                )
            else:
                response_content = await self._generate_general_response(
                    query, architecture_analysis, standards_context, search_results
                )

            # Extract sources
            sources = self._extract_sources(search_results)

            processing_time = time.time() - start_time

            # Create response
            response = AgentResponse(
                agent_type=self.agent_type,
                content=response_content,
                confidence=self._calculate_confidence(architecture_analysis, search_results),
                sources=sources,
                processing_time=processing_time,
                metadata={
                    "query_type": query_type,
                    "search_results_count": len(search_results),
                    "patterns_detected": [p.value for p in architecture_analysis.patterns_detected],
                    "solid_compliance": {
                        p.value: score for p, score in architecture_analysis.solid_compliance.items()
                    },
                    "analysis_confidence": architecture_analysis.confidence,
                },
            )

            # Update statistics
            self._update_stats(processing_time, True)

            logger.info(f"Technical architecture query processed successfully in {processing_time:.2f}s")
            return response

        except Exception as e:
            processing_time = time.time() - start_time
            self._update_stats(processing_time, False)

            logger.error(f"Error processing technical architecture query: {e}")
            raise AgentError(
                f"Failed to process technical architecture query: {e}",
                agent_type=self.agent_type.value,
                details={"query": query[:100]},
            ) from e

    def _classify_query_type(self, query: str) -> str:
        """Classify the type of architecture query."""
        query_lower = query.lower()

        if any(keyword in query_lower for keyword in ["analyze", "analysis", "assess", "review"]):
            return "analysis"

        if any(keyword in query_lower for keyword in ["design", "create", "implement", "build"]):
            return "design"

        if any(keyword in query_lower for keyword in ["validate", "check", "compliance", "solid"]):
            return "validation"

        if any(keyword in query_lower for keyword in ["standards", "rules", "guidelines", "best practices"]):
            return "standards"

        return "general"

    async def _perform_search(self, query: str, context: AgentContext) -> list[Any]:
        """Perform search for relevant code and documentation."""
        try:
            # Use existing search results if available
            if context.search_results:
                logger.debug("Using existing search results from context")
                return context.search_results[: self.max_search_results]

            # Enhance query for architecture-focused search
            enhanced_query = self._enhance_query_for_search(query)

            # Perform search with filters for architecture-relevant content
            filters = {"file_types": [".py", ".md", ".rst", ".txt"]}

            # Add repository filter if available
            if context.repository_context and "repository_url" in context.repository_context:
                filters["repository"] = context.repository_context["repository_url"]

            # Perform search
            search_results = await self.ingestion_pipeline.search_repository(
                query=enhanced_query, top_k=self.max_search_results, filters=filters
            )

            logger.debug(f"Found {len(search_results)} search results for architecture query")
            return search_results

        except Exception as e:
            logger.warning(f"Search failed, using empty results: {e}")
            return []

    def _enhance_query_for_search(self, query: str) -> str:
        """Enhance query to find architecture-relevant content."""
        # Add architecture-related terms to improve search relevance
        architecture_terms = [
            "class",
            "function",
            "module",
            "design",
            "pattern",
            "architecture",
            "structure",
            "component",
        ]

        # Add relevant terms based on query content
        enhanced_terms = []
        query_lower = query.lower()

        if "solid" in query_lower:
            enhanced_terms.extend(["principle", "responsibility", "dependency"])

        if "pattern" in query_lower:
            enhanced_terms.extend(["factory", "strategy", "singleton", "observer"])

        if "design" in query_lower:
            enhanced_terms.extend(["architecture", "component", "interface"])

        # Combine original query with enhancement terms
        if enhanced_terms:
            return f"{query} {' '.join(enhanced_terms[:3])}"  # Limit to 3 additional terms

        return query

    async def _generate_analysis_response(
        self, query: str, analysis: Any, search_results: list[Any]
    ) -> str:
        """Generate architecture analysis response."""
        # Create system prompt for analysis
        system_prompt = """You are a Technical Design Architect analyzing codebase architecture.

Your role is to:
- Analyze architectural patterns and design quality
- Assess SOLID principles compliance
- Identify strengths and areas for improvement
- Provide actionable recommendations

Focus on:
- Code organization and structure
- Design patterns usage
- Maintainability and extensibility
- Compliance with best practices

Provide clear, actionable insights with specific examples."""

        # Create user prompt with analysis data
        user_prompt = f"""
# Architecture Analysis Request

**Query:** {query}

## Current Architecture Analysis

**Patterns Detected:** {', '.join(p.value for p in analysis.patterns_detected)}

**SOLID Compliance Scores:**
{self._format_solid_scores(analysis.solid_compliance)}

**Quality Metrics:**
- Total Components: {analysis.code_quality_metrics.get('total_components', 0)}
- Average Complexity: {analysis.code_quality_metrics.get('average_complexity', 0):.1f}
- Components with Violations: {analysis.code_quality_metrics.get('components_with_violations', 0)}

**Identified Concerns:**
{self._format_list(analysis.concerns)}

**Identified Strengths:**
{self._format_list(analysis.strengths)}

**Recommendations:**
{self._format_list(analysis.recommendations)}

Please provide a comprehensive architecture analysis addressing the query.
"""

        # Generate response using LLM
        llm_response = await self.llm_client.generate_response(
            prompt=user_prompt, system_prompt=system_prompt
        )

        return llm_response.content

    async def _generate_design_response(
        self, query: str, analysis: Any, standards_context: Any
    ) -> str:
        """Generate design recommendation response."""
        # Determine design type based on query
        design_type = self._determine_design_type(query)

        # Extract requirements from query
        requirements = self._extract_requirements_from_query(query)

        # Generate design document
        design_doc = self.designer.generate_design(
            design_type=design_type,
            requirements=requirements,
            current_analysis=analysis,
            project_standards=standards_context.__dict__,
        )

        # Create system prompt for design
        system_prompt = """You are a Technical Design Architect creating system designs.

Your role is to:
- Generate comprehensive technical designs
- Ensure alignment with project standards
- Apply appropriate design patterns
- Provide implementation guidance

Focus on:
- Clear architectural decisions
- SOLID principles compliance
- Maintainable and extensible designs
- Practical implementation steps

Provide detailed, actionable design recommendations."""

        # Create user prompt with design document
        user_prompt = f"""
# Design Generation Request

**Query:** {query}

## Generated Design Document

**Type:** {design_doc.design_type.value}
**Title:** {design_doc.title}

**Overview:** {design_doc.overview}

**Recommendations:**
{self._format_design_recommendations(design_doc.recommendations)}

**Compliance Notes:**
{self._format_list(design_doc.compliance_notes)}

Please provide a comprehensive design response based on this analysis.
"""

        # Generate response using LLM
        llm_response = await self.llm_client.generate_response(
            prompt=user_prompt, system_prompt=system_prompt
        )

        return llm_response.content

    async def _generate_validation_response(
        self, query: str, analysis: Any, search_results: list[Any]
    ) -> str:
        """Generate SOLID validation response."""
        # Get component analysis from search results
        components = []
        for result in search_results:
            component = self.analyzer._analyze_component(result)
            if component:
                components.append(component)

        # Validate SOLID compliance
        validation_report = self.validator.validate_architecture(analysis, components)

        # Create system prompt for validation
        system_prompt = """You are a Technical Design Architect validating SOLID principles compliance.

Your role is to:
- Assess SOLID principles compliance
- Identify specific violations and issues
- Provide remediation recommendations
- Explain the impact of violations

Focus on:
- Clear explanation of SOLID principles
- Specific code quality issues
- Actionable improvement steps
- Priority-based recommendations

Provide detailed validation results with practical guidance."""

        # Create user prompt with validation report
        user_prompt = f"""
# SOLID Validation Request

**Query:** {query}

## Validation Report

**Overall Score:** {validation_report.overall_score:.1%}

**Principle Scores:**
{self._format_principle_scores(validation_report.principle_scores)}

**Issues Found:** {len(validation_report.issues)}
- Critical: {validation_report.summary.get('critical_issues', 0)}
- High: {validation_report.summary.get('high_issues', 0)}
- Medium: {validation_report.summary.get('medium_issues', 0)}

**Component Status:**
- Compliant: {len(validation_report.compliant_components)}
- Non-compliant: {len(validation_report.non_compliant_components)}

**Key Issues:**
{self._format_validation_issues(validation_report.issues[:5])}

**Recommendations:**
{self._format_list(validation_report.recommendations)}

Please provide a comprehensive validation analysis addressing the query.
"""

        # Generate response using LLM
        llm_response = await self.llm_client.generate_response(
            prompt=user_prompt, system_prompt=system_prompt
        )

        return llm_response.content

    async def _generate_standards_response(self, query: str, standards_context: Any) -> str:
        """Generate project standards response."""
        # Get standards summary
        standards_summary = self.standards_manager.get_standards_summary()

        # Create system prompt for standards
        system_prompt = """You are a Technical Design Architect explaining project standards.

Your role is to:
- Explain project standards and guidelines
- Provide compliance guidance
- Clarify architectural constraints
- Recommend best practices

Focus on:
- Clear explanation of standards
- Practical application guidance
- Compliance requirements
- Implementation examples

Provide authoritative guidance based on project documentation."""

        # Create user prompt with standards information
        user_prompt = f"""
# Project Standards Request

**Query:** {query}

## Project Standards Summary

**Total Rules:** {standards_summary['total_rules']}
- Mandatory: {standards_summary['mandatory_rules']}
- Recommended: {standards_summary['recommended_rules']}
- Guidance: {standards_summary['guidance_rules']}

**Design Principles:**
{self._format_list(standards_summary['design_principles'])}

**Technology Stack:**
{self._format_dict(standards_summary['technology_stack'])}

**Coding Standards:**
{self._format_dict(standards_summary['coding_standards'])}

**Testing Requirements:**
{self._format_dict(standards_summary['testing_requirements'])}

**Architectural Constraints:**
{self._format_list(standards_summary['architectural_constraints'])}

Please provide comprehensive guidance on project standards addressing the query.
"""

        # Generate response using LLM
        llm_response = await self.llm_client.generate_response(
            prompt=user_prompt, system_prompt=system_prompt
        )

        return llm_response.content

    async def _generate_general_response(
        self, query: str, analysis: Any, standards_context: Any, search_results: list[Any]
    ) -> str:
        """Generate general technical architecture response."""
        # Create comprehensive system prompt
        system_prompt = """You are a Technical Design Architect providing comprehensive technical guidance.

Your role is to:
- Analyze technical architecture questions
- Provide design recommendations
- Ensure standards compliance
- Offer practical implementation guidance

Focus on:
- Comprehensive technical analysis
- Actionable recommendations
- Best practices application
- Clear implementation steps

Provide thorough, practical guidance for technical architecture decisions."""

        # Create comprehensive user prompt
        user_prompt = f"""
# Technical Architecture Request

**Query:** {query}

## Architecture Context

**Current Patterns:** {', '.join(p.value for p in analysis.patterns_detected)}

**SOLID Compliance:** {analysis.solid_compliance}

**Quality Metrics:** {analysis.code_quality_metrics}

**Search Results:** {len(search_results)} relevant components found

**Project Standards Available:** {len(standards_context.rules)} rules and guidelines

Please provide comprehensive technical architecture guidance addressing the query.
"""

        # Generate response using LLM
        llm_response = await self.llm_client.generate_response(
            prompt=user_prompt, system_prompt=system_prompt
        )

        return llm_response.content

    def _determine_design_type(self, query: str) -> DesignType:
        """Determine the type of design document to generate."""
        query_lower = query.lower()

        if "api" in query_lower:
            return DesignType.API_DESIGN
        elif "component" in query_lower:
            return DesignType.COMPONENT_DESIGN
        elif "refactor" in query_lower:
            return DesignType.REFACTORING_PLAN
        else:
            return DesignType.ARCHITECTURE_OVERVIEW

    def _extract_requirements_from_query(self, query: str) -> list[str]:
        """Extract requirements from the query."""
        # Simple requirement extraction - could be enhanced with NLP
        requirements = [query]

        # Add common requirements based on query content
        query_lower = query.lower()

        if "agent" in query_lower:
            requirements.append("Implement agent following established patterns")

        if "api" in query_lower:
            requirements.append("Design RESTful API endpoints")

        if "test" in query_lower:
            requirements.append("Include comprehensive testing strategy")

        return requirements

    def _extract_sources(self, search_results: list[Any]) -> list[str]:
        """Extract source citations from search results."""
        sources = []
        for result in search_results[:10]:  # Limit to top 10 sources
            try:
                file_path = result.chunk.file_metadata.file_path
            except AttributeError:
                file_path = "Unknown file"
            sources.append(file_path)
        return sources

    def _calculate_confidence(self, analysis: Any, search_results: list[Any]) -> float:
        """Calculate confidence in the response."""
        # Base confidence on analysis quality
        analysis_confidence = analysis.confidence

        # Adjust based on search results quality
        search_confidence = min(len(search_results) / 10, 1.0)  # Max confidence at 10+ results

        # Adjust based on pattern detection
        pattern_confidence = min(len(analysis.patterns_detected) / 3, 1.0)

        return (analysis_confidence + search_confidence + pattern_confidence) / 3

    def _format_solid_scores(self, solid_compliance: dict) -> str:
        """Format SOLID compliance scores."""
        if not solid_compliance:
            return "No SOLID compliance data available"

        lines = []
        for principle, score in solid_compliance.items():
            principle_name = principle.value.replace("_", " ").title()
            lines.append(f"- {principle_name}: {score:.1%}")

        return "\n".join(lines)

    def _format_list(self, items: list[str]) -> str:
        """Format a list of items."""
        if not items:
            return "None"
        return "\n".join(f"- {item}" for item in items)

    def _format_dict(self, data: dict) -> str:
        """Format a dictionary."""
        if not data:
            return "None"
        return "\n".join(f"- {key}: {value}" for key, value in data.items())

    def _format_design_recommendations(self, recommendations: list) -> str:
        """Format design recommendations."""
        if not recommendations:
            return "No recommendations available"

        lines = []
        for i, rec in enumerate(recommendations, 1):
            lines.append(f"{i}. **{rec.title}** ({rec.priority.value})")
            lines.append(f"   {rec.description}")
            if rec.implementation_steps:
                lines.append(f"   Steps: {', '.join(rec.implementation_steps[:3])}")
            lines.append("")

        return "\n".join(lines)

    def _format_principle_scores(self, principle_scores: dict) -> str:
        """Format SOLID principle scores."""
        if not principle_scores:
            return "No principle scores available"

        lines = []
        for principle, score in principle_scores.items():
            principle_name = principle.value.replace("_", " ").title()
            lines.append(f"- {principle_name}: {score:.1%}")

        return "\n".join(lines)

    def _format_validation_issues(self, issues: list) -> str:
        """Format validation issues."""
        if not issues:
            return "No issues found"

        lines = []
        for issue in issues:
            lines.append(f"- **{issue.title}** ({issue.severity.value})")
            lines.append(f"  Component: {issue.component}")
            lines.append(f"  {issue.description}")
            lines.append("")

        return "\n".join(lines)
