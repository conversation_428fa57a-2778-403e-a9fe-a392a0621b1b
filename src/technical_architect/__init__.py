"""
Technical Design Architect Agent Module

Generates system designs and architecture documentation aligned with project standards.
"""

from .agent import TechnicalArchitectAgent
from .analyzer import (
    ArchitectureAnalysis,
    ArchitectureAnalyzer,
    ArchitecturePattern,
    ComponentAnalysis,
    SOLIDPrinciple,
)
from .designer import (
    DesignDocument,
    DesignPriority,
    DesignRecommendation,
    DesignType,
    TechnicalDesigner,
)
from .standards import (
    DocumentType,
    ProjectStandardsManager,
    StandardsContext,
    StandardsPriority,
    StandardsRule,
)
from .validator import (
    SOLIDValidator,
    ValidationIssue,
    ValidationReport,
    ValidationSeverity,
)

__all__ = [
    "TechnicalArchitectAgent",
    "ArchitectureAnalysis",
    "ArchitectureAnalyzer",
    "ArchitecturePattern",
    "ComponentAnalysis",
    "SOLIDPrinciple",
    "DesignDocument",
    "DesignPriority",
    "DesignRecommendation",
    "DesignType",
    "TechnicalDesigner",
    "DocumentType",
    "ProjectStandardsManager",
    "StandardsContext",
    "StandardsPriority",
    "StandardsRule",
    "SOLIDValidator",
    "ValidationIssue",
    "ValidationReport",
    "ValidationSeverity",
]
