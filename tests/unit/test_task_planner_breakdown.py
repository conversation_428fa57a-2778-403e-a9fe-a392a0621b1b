"""
Unit tests for Task Planner requirement breakdown engine.
"""

import pytest
from unittest.mock import Mock, patch

from src.task_planner.breakdown import RequirementBreakdownEngine
from src.task_planner.models import TaskPriority, TaskType


class TestRequirementBreakdownEngine:
    """Test RequirementBreakdownEngine functionality."""

    @pytest.fixture
    def breakdown_engine(self):
        """Create a breakdown engine for testing."""
        return RequirementBreakdownEngine()

    def test_initialization(self, breakdown_engine):
        """Test breakdown engine initialization."""
        assert breakdown_engine is not None
        assert "analysis" in breakdown_engine.task_patterns
        assert "implementation" in breakdown_engine.task_patterns
        assert "testing" in breakdown_engine.task_patterns

    def test_break_down_simple_requirement(self, breakdown_engine):
        """Test breaking down a simple requirement."""
        requirement = "Implement a user authentication system"
        
        tasks = breakdown_engine.break_down_requirement(requirement)
        
        assert len(tasks) > 0
        # Should generate 5-phase tasks
        task_titles = [task.title for task in tasks]
        assert any("Discovery & Analysis" in title for title in task_titles)
        assert any("Task Planning" in title for title in task_titles)
        assert any("Implementation" in title for title in task_titles)

    def test_break_down_multiple_requirements(self, breakdown_engine):
        """Test breaking down multiple requirements."""
        requirements = [
            "Implement user authentication",
            "Create user dashboard",
            "Add data export functionality"
        ]
        
        tasks = breakdown_engine.break_down_requirements(requirements)
        
        assert len(tasks) > len(requirements)  # Should generate multiple tasks per requirement
        
        # Check that task IDs are unique
        task_ids = [task.id for task in tasks]
        assert len(task_ids) == len(set(task_ids))
        
        # Should include integration tasks for multiple requirements
        task_titles = [task.title for task in tasks]
        assert any("Integration" in title for title in task_titles)

    def test_analyze_requirement_complexity(self, breakdown_engine):
        """Test requirement complexity analysis."""
        # Simple requirement
        simple_req = "Add a button to the page"
        simple_analysis = breakdown_engine._analyze_requirement(simple_req)
        assert simple_analysis["complexity"] in ["low", "medium"]
        
        # Complex requirement
        complex_req = "Implement a sophisticated distributed microservice architecture with advanced scalability"
        complex_analysis = breakdown_engine._analyze_requirement(complex_req)
        assert complex_analysis["complexity"] == "high"

    def test_analyze_requirement_priority(self, breakdown_engine):
        """Test requirement priority analysis."""
        # Critical requirement
        critical_req = "Fix critical security vulnerability that must be addressed urgently"
        critical_analysis = breakdown_engine._analyze_requirement(critical_req)
        assert critical_analysis["priority"] == "critical"
        
        # Low priority requirement
        low_req = "Add optional nice to have feature for future enhancement"
        low_analysis = breakdown_engine._analyze_requirement(low_req)
        # Priority detection is heuristic-based, so we allow medium as well
        assert low_analysis["priority"] in ["low", "medium"]

    def test_analyze_requirement_task_types(self, breakdown_engine):
        """Test task type identification."""
        # Analysis requirement
        analysis_req = "Research and analyze the current system architecture"
        analysis_result = breakdown_engine._analyze_requirement(analysis_req)
        assert "analysis" in analysis_result["task_types"]
        
        # Implementation requirement
        impl_req = "Build and implement the new API endpoints"
        impl_result = breakdown_engine._analyze_requirement(impl_req)
        assert "implementation" in impl_result["task_types"]
        
        # Testing requirement
        test_req = "Test and validate the new functionality"
        test_result = breakdown_engine._analyze_requirement(test_req)
        assert "testing" in test_result["task_types"]

    def test_generate_5_phase_tasks(self, breakdown_engine):
        """Test 5-phase methodology task generation."""
        requirement = "Implement user authentication system"
        analysis = {
            "task_types": ["analysis", "implementation", "testing"],
            "complexity": "medium",
            "priority": "high",
            "estimated_effort": 40.0,
            "keywords": ["user", "authentication", "system"],
            "entities": [],
            "risks": [],
        }
        context = {"requirement_index": 0}
        
        tasks = breakdown_engine._generate_5_phase_tasks(requirement, analysis, context)
        
        # Should generate tasks for each phase
        assert len(tasks) >= 4  # At least Discovery, Planning, Implementation, Documentation
        
        # Check phase tags
        phase_tags = []
        for task in tasks:
            phase_tags.extend([tag for tag in task.tags if tag.startswith("phase-")])
        
        assert "phase-1" in phase_tags  # Discovery & Analysis
        assert "phase-2" in phase_tags  # Task Planning
        assert "phase-3" in phase_tags  # Implementation
        
        # Check dependencies between phases
        for i in range(1, len(tasks)):
            if tasks[i].dependencies:
                # Should depend on previous phase
                assert tasks[i-1].id in [dep.task_id for dep in tasks[i].dependencies]

    def test_generate_implementation_tasks(self, breakdown_engine):
        """Test implementation task generation."""
        requirement = "Create user component and API endpoints"
        analysis = {
            "task_types": ["implementation"],
            "complexity": "medium",
            "priority": "high",
            "estimated_effort": 24.0,
            "keywords": ["user", "component", "api"],
            "entities": [
                {"name": "UserComponent", "type": "component"},
                {"name": "UserAPI", "type": "api"}
            ],
            "risks": [],
        }
        context = {"requirement_index": 0}
        
        tasks = breakdown_engine._generate_implementation_tasks(requirement, analysis, context)
        
        # Should generate tasks for each entity
        assert len(tasks) >= 2
        
        # Check that component and API tasks are created
        task_titles = [task.title for task in tasks]
        assert any("UserComponent" in title for title in task_titles)
        assert any("UserAPI" in title for title in task_titles)

    def test_generate_quality_tasks(self, breakdown_engine):
        """Test quality task generation."""
        requirement = "Implement user authentication"
        analysis = {
            "task_types": ["implementation", "testing"],
            "complexity": "high",
            "priority": "high",
            "estimated_effort": 40.0,
            "keywords": ["user", "authentication"],
            "entities": [],
            "risks": [],
        }
        context = {"requirement_index": 0}
        
        tasks = breakdown_engine._generate_quality_tasks(requirement, analysis, context)
        
        # Should generate unit tests
        assert len(tasks) >= 1
        
        # For high complexity, should also generate integration tests
        task_types = [task.task_type for task in tasks]
        assert TaskType.TESTING in task_types
        
        # Check acceptance criteria
        for task in tasks:
            if task.task_type == TaskType.TESTING:
                assert len(task.acceptance_criteria) > 0

    def test_extract_keywords(self, breakdown_engine):
        """Test keyword extraction."""
        requirement = "Implement a secure user authentication system with OAuth integration"
        
        keywords = breakdown_engine._extract_keywords(requirement)
        
        assert "implement" in keywords
        assert "secure" in keywords
        assert "user" in keywords
        assert "authentication" in keywords
        assert "system" in keywords
        assert "oauth" in keywords
        assert "integration" in keywords
        
        # Should filter out stop words
        assert "a" not in keywords
        assert "with" not in keywords

    def test_extract_entities(self, breakdown_engine):
        """Test entity extraction."""
        requirement = "Create UserService component and UserAPI endpoints for authentication"
        
        entities = breakdown_engine._extract_entities(requirement)
        
        # Should extract components and APIs
        entity_names = [entity["name"] for entity in entities]
        entity_types = [entity["type"] for entity in entities]
        
        assert "UserService" in entity_names
        assert "UserAPI" in entity_names
        assert "component" in entity_types
        assert "api" in entity_types

    def test_estimate_effort(self, breakdown_engine):
        """Test effort estimation."""
        # Low complexity
        low_analysis = {
            "complexity": "low",
            "task_types": ["implementation"]
        }
        low_effort = breakdown_engine._estimate_effort(low_analysis)
        assert low_effort < 8.0  # Less than base effort
        
        # High complexity with multiple task types
        high_analysis = {
            "complexity": "high",
            "task_types": ["analysis", "design", "implementation", "testing"]
        }
        high_effort = breakdown_engine._estimate_effort(high_analysis)
        assert high_effort > 16.0  # More than base effort

    def test_identify_risks(self, breakdown_engine):
        """Test risk identification."""
        # High complexity requirement
        high_complexity_req = "Implement sophisticated distributed system"
        high_analysis = breakdown_engine._analyze_requirement(high_complexity_req)
        
        risks = breakdown_engine._identify_risks(high_complexity_req, high_analysis)
        
        # Should identify complexity risk
        risk_descriptions = [risk["description"] for risk in risks]
        assert any("complexity" in desc.lower() for desc in risk_descriptions)
        
        # Integration requirement
        integration_req = "Integrate with multiple external systems"
        integration_analysis = breakdown_engine._analyze_requirement(integration_req)
        
        integration_risks = breakdown_engine._identify_risks(integration_req, integration_analysis)
        
        # Should identify integration risk (may not always detect, so check if any risks found)
        assert len(integration_risks) >= 0  # At least should not fail

    def test_generate_integration_tasks(self, breakdown_engine):
        """Test integration task generation for multiple requirements."""
        requirements = [
            "Implement user authentication",
            "Create user dashboard",
            "Add notification system"
        ]
        
        # Create some mock tasks
        mock_tasks = []
        for i, req in enumerate(requirements):
            tasks = breakdown_engine.break_down_requirement(req)
            mock_tasks.extend(tasks)
        
        context = {}
        integration_tasks = breakdown_engine._generate_integration_tasks(requirements, mock_tasks, context)
        
        # Should generate integration tasks for multiple requirements
        assert len(integration_tasks) > 0
        
        # Check integration task properties
        integration_task = integration_tasks[0]
        assert "Integration" in integration_task.title
        assert integration_task.task_type == TaskType.IMPLEMENTATION
        assert integration_task.priority == TaskPriority.HIGH

    def test_context_preservation(self, breakdown_engine):
        """Test that context is preserved across requirement breakdown."""
        requirements = ["First requirement", "Second requirement"]
        
        tasks = breakdown_engine.break_down_requirements(requirements)
        
        # Should have unique task IDs
        task_ids = [task.id for task in tasks]
        assert len(task_ids) == len(set(task_ids))
        
        # Task IDs should be sequential and start with T001
        assert any(task.id.startswith("T001") for task in tasks)
        # Later tasks should have higher numbers
        task_numbers = [int(task.id[1:]) for task in tasks if task.id[1:].isdigit()]
        assert max(task_numbers) > min(task_numbers) if task_numbers else True

    def test_empty_requirements(self, breakdown_engine):
        """Test handling of empty requirements."""
        tasks = breakdown_engine.break_down_requirements([])
        assert len(tasks) == 0
        
        tasks = breakdown_engine.break_down_requirement("")
        # Should still generate some basic structure
        assert len(tasks) >= 0

    def test_requirement_with_context(self, breakdown_engine):
        """Test requirement breakdown with additional context."""
        requirement = "Add user authentication"
        context = {
            "requirement_index": 1,
            "total_requirements": 3,
            "project_type": "web_application"
        }
        
        tasks = breakdown_engine.break_down_requirement(requirement, context)
        
        assert len(tasks) > 0
        # Context should influence task generation
        for task in tasks:
            assert task.id is not None
            assert len(task.title) > 0
