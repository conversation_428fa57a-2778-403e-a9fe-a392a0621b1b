"""
Unit tests for embedding clients.

Tests the embedding client implementations including OpenAI and local clients
with mocking for external dependencies.
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch
import asyncio

from src.ingestion.embedding.client import OpenAIEmbeddingClient, EmbeddingClientFactory
from src.ingestion.embedding.local import LocalEmbeddingClient
from src.ingestion.exceptions import IngestionError
from src.config import Settings


@pytest.fixture
def mock_settings():
    """Create mock settings for testing."""
    settings = Mock(spec=Settings)
    settings.openai_api_key = "test-api-key"
    settings.embedding_model = "text-embedding-3-large"
    settings.embedding_provider = "openai"
    settings.embedding_batch_size = 10
    settings.embedding_max_retries = 3
    settings.embedding_timeout = 60
    settings.embedding_normalize = True
    return settings


@pytest.fixture
def mock_local_settings():
    """Create mock settings for local embedding testing."""
    settings = Mock(spec=Settings)
    settings.embedding_provider = "local"
    settings.embedding_batch_size = 5
    settings.embedding_normalize = True
    settings.local_embedding_model = "all-MiniLM-L6-v2"
    return settings


class TestOpenAIEmbeddingClient:
    """Test cases for OpenAIEmbeddingClient."""
    
    def test_initialization(self, mock_settings):
        """Test client initialization."""
        client = OpenAIEmbeddingClient(mock_settings)
        
        assert client.model == "text-embedding-3-large"
        assert client.batch_size == 10
        assert client.max_retries == 3
        assert client.timeout == 60
    
    def test_initialization_invalid_model(self, mock_settings):
        """Test initialization with invalid model."""
        mock_settings.embedding_model = "invalid-model"
        
        with pytest.raises(IngestionError) as exc_info:
            OpenAIEmbeddingClient(mock_settings)
        
        assert "Unsupported embedding model" in str(exc_info.value)
    
    def test_get_embedding_dimension(self, mock_settings):
        """Test getting embedding dimension."""
        client = OpenAIEmbeddingClient(mock_settings)
        
        dimension = client.get_embedding_dimension()
        assert dimension == 3072  # text-embedding-3-large dimension
    
    def test_get_model_info(self, mock_settings):
        """Test getting model information."""
        client = OpenAIEmbeddingClient(mock_settings)
        
        info = client.get_model_info()
        assert info["provider"] == "openai"
        assert info["model"] == "text-embedding-3-large"
        assert info["dimension"] == 3072
    
    def test_estimate_cost(self, mock_settings):
        """Test cost estimation."""
        client = OpenAIEmbeddingClient(mock_settings)
        
        texts = ["short text", "longer text with more words"]
        cost = client.estimate_cost(texts)
        
        assert cost > 0
        assert isinstance(cost, float)
    
    @pytest.mark.asyncio
    async def test_generate_embeddings_success(self, mock_settings):
        """Test successful embedding generation."""
        client = OpenAIEmbeddingClient(mock_settings)
        
        # Mock the OpenAI client response
        mock_response = Mock()
        mock_response.data = [
            Mock(embedding=[0.1, 0.2, 0.3] * 1024),  # 3072 dimensions
            Mock(embedding=[0.4, 0.5, 0.6] * 1024),
        ]
        mock_response.usage.total_tokens = 100
        
        with patch.object(client.client.embeddings, 'create', new_callable=AsyncMock) as mock_create:
            mock_create.return_value = mock_response
            
            texts = ["test text 1", "test text 2"]
            embeddings = await client.generate_embeddings(texts)
            
            assert len(embeddings) == 2
            assert len(embeddings[0]) == 3072
            assert len(embeddings[1]) == 3072
            
            # Verify API was called correctly
            mock_create.assert_called_once_with(
                model="text-embedding-3-large",
                input=texts,
                timeout=60
            )
    
    @pytest.mark.asyncio
    async def test_generate_embeddings_empty_input(self, mock_settings):
        """Test embedding generation with empty input."""
        client = OpenAIEmbeddingClient(mock_settings)
        
        embeddings = await client.generate_embeddings([])
        assert embeddings == []
    
    @pytest.mark.asyncio
    async def test_generate_single_embedding(self, mock_settings):
        """Test single embedding generation."""
        client = OpenAIEmbeddingClient(mock_settings)
        
        mock_response = Mock()
        mock_response.data = [Mock(embedding=[0.1, 0.2, 0.3] * 1024)]
        mock_response.usage.total_tokens = 50
        
        with patch.object(client.client.embeddings, 'create', new_callable=AsyncMock) as mock_create:
            mock_create.return_value = mock_response
            
            embedding = await client.generate_single_embedding("test text")
            
            assert len(embedding) == 3072
            assert embedding == [0.1, 0.2, 0.3] * 1024
    
    @pytest.mark.asyncio
    async def test_rate_limit_handling(self, mock_settings):
        """Test rate limit error handling."""
        client = OpenAIEmbeddingClient(mock_settings)

        # Mock rate limit error
        import openai
        rate_limit_error = openai.RateLimitError(
            message="Rate limit exceeded",
            response=Mock(status_code=429),
            body={}
        )

        with patch.object(client.client.embeddings, 'create', new_callable=AsyncMock) as mock_create:
            mock_create.side_effect = rate_limit_error

            with pytest.raises(IngestionError) as exc_info:
                await client.generate_embeddings(["test text"])

            assert "Rate limit exceeded" in str(exc_info.value)
    
    def test_stats_tracking(self, mock_settings):
        """Test statistics tracking."""
        client = OpenAIEmbeddingClient(mock_settings)
        
        # Initial stats
        stats = client.get_stats()
        assert stats["total_requests"] == 0
        assert stats["total_tokens"] == 0
        assert stats["total_cost"] == 0.0
        
        # Update stats manually (simulating successful request)
        client._update_stats(100, 2)
        
        updated_stats = client.get_stats()
        assert updated_stats["total_requests"] == 1
        assert updated_stats["total_tokens"] == 100
        assert updated_stats["total_cost"] > 0
        
        # Reset stats
        client.reset_stats()
        reset_stats = client.get_stats()
        assert reset_stats["total_requests"] == 0


class TestLocalEmbeddingClient:
    """Test cases for LocalEmbeddingClient."""
    
    def test_initialization(self, mock_local_settings):
        """Test local client initialization."""
        client = LocalEmbeddingClient(mock_local_settings)
        
        assert client.model_name == "all-MiniLM-L6-v2"
        assert client.batch_size == 5
        assert client.normalize_embeddings is True
    
    def test_get_embedding_dimension(self, mock_local_settings):
        """Test getting embedding dimension for local model."""
        client = LocalEmbeddingClient(mock_local_settings)
        
        dimension = client.get_embedding_dimension()
        assert dimension == 384  # all-MiniLM-L6-v2 dimension
    
    def test_get_model_info(self, mock_local_settings):
        """Test getting local model information."""
        client = LocalEmbeddingClient(mock_local_settings)
        
        info = client.get_model_info()
        assert info["provider"] == "local"
        assert info["model"] == "all-MiniLM-L6-v2"
        assert info["dimension"] == 384
    
    def test_estimate_cost(self, mock_local_settings):
        """Test cost estimation for local model (should be free)."""
        client = LocalEmbeddingClient(mock_local_settings)
        
        texts = ["test text 1", "test text 2"]
        cost = client.estimate_cost(texts)
        
        assert cost == 0.0  # Local embeddings are free
    
    @pytest.mark.asyncio
    async def test_generate_embeddings_without_model(self, mock_local_settings):
        """Test embedding generation without sentence-transformers installed."""
        client = LocalEmbeddingClient(mock_local_settings)

        # Mock ImportError for sentence-transformers by patching the import in _load_model
        with patch.object(client, '_load_model', side_effect=IngestionError("sentence-transformers library is required")):
            with pytest.raises(IngestionError) as exc_info:
                await client.generate_embeddings(["test text"])

            assert "sentence-transformers library is required" in str(exc_info.value)
    
    def test_model_unloading(self, mock_local_settings):
        """Test model unloading functionality."""
        client = LocalEmbeddingClient(mock_local_settings)
        
        # Initially no model loaded
        assert client._model is None
        
        # Simulate model loading
        client._model = Mock()
        client._stats["model_loaded"] = True
        
        # Unload model
        client.unload_model()
        
        assert client._model is None
        assert client._stats["model_loaded"] is False


class TestEmbeddingClientFactory:
    """Test cases for EmbeddingClientFactory."""
    
    def test_create_openai_client(self, mock_settings):
        """Test creating OpenAI client through factory."""
        client = EmbeddingClientFactory.create_client(mock_settings)
        
        assert isinstance(client, OpenAIEmbeddingClient)
        assert client.model == "text-embedding-3-large"
    
    def test_create_local_client(self, mock_local_settings):
        """Test creating local client through factory."""
        client = EmbeddingClientFactory.create_client(mock_local_settings)
        
        assert isinstance(client, LocalEmbeddingClient)
        assert client.model_name == "all-MiniLM-L6-v2"
    
    def test_unsupported_provider(self, mock_settings):
        """Test creating client with unsupported provider."""
        mock_settings.embedding_provider = "unsupported"
        
        with pytest.raises(IngestionError) as exc_info:
            EmbeddingClientFactory.create_client(mock_settings)
        
        assert "Unsupported embedding provider" in str(exc_info.value)
    
    def test_get_available_providers(self):
        """Test getting available providers."""
        providers = EmbeddingClientFactory.get_available_providers()
        
        assert "openai" in providers
        assert "local" in providers
        assert len(providers) >= 2
    
    def test_validate_openai_config(self, mock_settings):
        """Test validating OpenAI configuration."""
        # Valid config
        assert EmbeddingClientFactory.validate_provider_config(mock_settings) is True
        
        # Invalid config (no API key)
        mock_settings.openai_api_key = None
        with pytest.raises(IngestionError) as exc_info:
            EmbeddingClientFactory.validate_provider_config(mock_settings)
        
        assert "OpenAI API key is required" in str(exc_info.value)
    
    def test_validate_local_config(self, mock_local_settings):
        """Test validating local configuration."""
        assert EmbeddingClientFactory.validate_provider_config(mock_local_settings) is True
