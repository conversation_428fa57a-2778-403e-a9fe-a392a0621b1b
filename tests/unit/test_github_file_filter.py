"""
Unit tests for GitHubFileFilter.

Tests the file filtering functionality including type filtering,
size limits, exclusion patterns, and priority scoring.
"""

import pytest
from unittest.mock import Mock
from pathlib import Path

from src.ingestion.github.filters import GitHubFileFilter
from src.config import Settings


@pytest.fixture
def mock_settings():
    """Create mock settings for testing."""
    settings = Mock(spec=Settings)
    settings.max_file_size = 1024 * 1024  # 1MB
    settings.allowed_file_types = ["py", "js", "ts", "md", "txt", "json"]
    return settings


@pytest.fixture
def file_filter(mock_settings):
    """Create GitHubFileFilter instance for testing."""
    return GitHubFileFilter(mock_settings)


class TestGitHubFileFilter:
    """Test cases for GitHubFileFilter."""
    
    def test_should_include_file_valid_python(self, file_filter):
        """Test including a valid Python file."""
        result = file_filter.should_include_file("src/main.py", 1024)
        assert result is True
    
    def test_should_include_file_valid_javascript(self, file_filter):
        """Test including a valid JavaScript file."""
        result = file_filter.should_include_file("app.js", 2048)
        assert result is True
    
    def test_should_include_file_valid_markdown(self, file_filter):
        """Test including a valid Markdown file."""
        result = file_filter.should_include_file("README.md", 512)
        assert result is True
    
    def test_should_exclude_file_too_large(self, file_filter):
        """Test excluding a file that's too large."""
        large_size = 2 * 1024 * 1024  # 2MB
        result = file_filter.should_include_file("large_file.py", large_size)
        assert result is False
    
    def test_should_exclude_file_invalid_extension(self, file_filter):
        """Test excluding a file with invalid extension."""
        result = file_filter.should_include_file("image.png", 1024)
        assert result is False
    
    def test_should_exclude_file_no_extension_not_common(self, file_filter):
        """Test excluding a file with no extension that's not common."""
        result = file_filter.should_include_file("random_file", 1024)
        assert result is False
    
    def test_should_include_file_no_extension_common(self, file_filter):
        """Test including a common file with no extension."""
        result = file_filter.should_include_file("README", 1024)
        assert result is True
    
    def test_should_include_file_dockerfile(self, file_filter):
        """Test including Dockerfile."""
        result = file_filter.should_include_file("Dockerfile", 1024)
        assert result is True
    
    def test_should_exclude_file_empty(self, file_filter):
        """Test excluding empty file when metadata indicates zero size."""
        metadata = {"size": 0}
        result = file_filter.should_include_file("empty.py", 1024, metadata)
        assert result is False
    
    def test_exclusion_patterns_node_modules(self, file_filter):
        """Test exclusion of node_modules directory."""
        result = file_filter.should_include_file("node_modules/package/index.js", 1024)
        assert result is False
    
    def test_exclusion_patterns_pycache(self, file_filter):
        """Test exclusion of __pycache__ directory."""
        result = file_filter.should_include_file("src/__pycache__/module.pyc", 1024)
        assert result is False
    
    def test_exclusion_patterns_git_directory(self, file_filter):
        """Test exclusion of .git directory."""
        result = file_filter.should_include_file(".git/config", 1024)
        assert result is False
    
    def test_exclusion_patterns_log_files(self, file_filter):
        """Test exclusion of log files."""
        result = file_filter.should_include_file("app.log", 1024)
        assert result is False
    
    def test_exclusion_patterns_binary_files(self, file_filter):
        """Test exclusion of binary files."""
        result = file_filter.should_include_file("app.exe", 1024)
        assert result is False
    
    def test_exclusion_patterns_media_files(self, file_filter):
        """Test exclusion of media files."""
        result = file_filter.should_include_file("image.jpg", 1024)
        assert result is False
    
    def test_filter_files_mixed_list(self, file_filter):
        """Test filtering a mixed list of files."""
        files = [
            "src/main.py",           # Should include
            "README.md",             # Should include
            "node_modules/pkg.js",   # Should exclude
            "image.png",             # Should exclude
            "config.json",           # Should include
            "__pycache__/cache.pyc", # Should exclude
            "app.log",               # Should exclude
            "script.js",             # Should include
        ]
        
        filtered = file_filter.filter_files(files)
        
        expected = ["src/main.py", "README.md", "config.json", "script.js"]
        assert filtered == expected
    
    def test_get_file_priority_docs_directory(self, file_filter):
        """Test priority boost for docs directory."""
        priority = file_filter.get_file_priority("docs/api.md")
        assert priority == 0.2
    
    def test_get_file_priority_readme_file(self, file_filter):
        """Test priority boost for README file."""
        priority = file_filter.get_file_priority("README.md")
        # README gets 0.15 for README pattern + 0.15 for README filename = 0.3
        assert priority == 0.3

    def test_get_file_priority_src_directory(self, file_filter):
        """Test priority boost for src directory."""
        priority = file_filter.get_file_priority("src/main.py")
        # src/ gets 0.05 + main.py gets 0.1 = 0.15
        assert abs(priority - 0.15) < 0.001

    def test_get_file_priority_test_file_penalty(self, file_filter):
        """Test priority penalty for test files."""
        priority = file_filter.get_file_priority("tests/test_main.py")
        # test penalty -0.05 but clamped to 0.0
        assert priority == 0.0
    
    def test_get_file_priority_main_entry_point(self, file_filter):
        """Test priority boost for main entry points."""
        priority = file_filter.get_file_priority("main.py")
        assert priority == 0.1
    
    def test_get_file_priority_config_file(self, file_filter):
        """Test priority boost for config files."""
        priority = file_filter.get_file_priority("config.json")
        assert priority == 0.1
    
    def test_get_file_priority_regular_file(self, file_filter):
        """Test no priority boost for regular files."""
        priority = file_filter.get_file_priority("utils.py")
        assert priority == 0.0
    
    def test_get_file_priority_negative_clamped(self, file_filter):
        """Test that negative priorities are clamped to zero."""
        # This would normally be negative due to test penalty
        priority = file_filter.get_file_priority("test_utils.py")
        assert priority == 0.0  # Should be clamped to 0
    
    def test_add_exclusion_pattern(self, file_filter):
        """Test adding a new exclusion pattern."""
        # Initially should include
        assert file_filter.should_include_file("custom/file.py", 1024) is True
        
        # Add exclusion
        file_filter.add_exclusion("custom/")
        
        # Now should exclude
        assert file_filter.should_include_file("custom/file.py", 1024) is False
    
    def test_remove_exclusion_pattern(self, file_filter):
        """Test removing an exclusion pattern."""
        # Add custom exclusion first
        file_filter.add_exclusion("temp/")
        assert file_filter.should_include_file("temp/file.py", 1024) is False
        
        # Remove exclusion
        file_filter.remove_exclusion("temp/")
        assert file_filter.should_include_file("temp/file.py", 1024) is True
    
    def test_get_filter_stats(self, file_filter):
        """Test getting filter statistics."""
        stats = file_filter.get_filter_stats()
        
        assert "max_file_size" in stats
        assert "allowed_file_types" in stats
        assert "exclusion_count" in stats
        assert "priority_boost_count" in stats
        
        assert stats["max_file_size"] == 1024 * 1024
        assert "py" in stats["allowed_file_types"]
        assert stats["exclusion_count"] > 0
        assert stats["priority_boost_count"] > 0
    
    def test_additional_exclusions(self, mock_settings):
        """Test file filter with additional exclusions."""
        additional_exclusions = {"custom_dir/", "*.backup"}
        file_filter = GitHubFileFilter(mock_settings, additional_exclusions)
        
        # Should exclude files matching additional patterns
        assert file_filter.should_include_file("custom_dir/file.py", 1024) is False
        assert file_filter.should_include_file("file.backup", 1024) is False
        
        # Should still include normal files
        assert file_filter.should_include_file("src/main.py", 1024) is True
    
    def test_case_insensitive_exclusions(self, file_filter):
        """Test that exclusions are case insensitive."""
        # Test with different cases
        assert file_filter.should_include_file("NODE_MODULES/pkg.js", 1024) is False
        assert file_filter.should_include_file("Node_Modules/pkg.js", 1024) is False
        assert file_filter.should_include_file("file.LOG", 1024) is False
        assert file_filter.should_include_file("FILE.EXE", 1024) is False
