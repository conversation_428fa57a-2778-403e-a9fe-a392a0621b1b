"""
Unit tests for Technical Architect Agent.
"""

import pytest
from unittest.mock import AsyncMock, Mock, patch

from src.agents.base import <PERSON><PERSON><PERSON><PERSON><PERSON>, AgentResponse, AgentType, ConversationContext
from src.agents.llm_client import LLMResponse
from src.config import Settings
from src.ingestion.base import SearchResult
from src.technical_architect.agent import TechnicalArchitectAgent
from src.technical_architect.analyzer import ArchitectureAnalysis, ArchitecturePattern
from src.technical_architect.designer import DesignType


class TestTechnicalArchitectAgent:
    """Test cases for TechnicalArchitectAgent."""

    @pytest.fixture
    def mock_settings(self):
        """Create mock settings."""
        settings = Mock(spec=Settings)
        settings.openai_api_key = "test-key"
        settings.openai_model = "gpt-4"
        settings.openai_temperature = 0.7
        settings.openai_max_tokens = 2000
        return settings

    @pytest.fixture
    def mock_llm_client(self):
        """Create mock LLM client."""
        client = AsyncMock()
        client.generate_response.return_value = LLMResponse(
            content="Test LLM response content",
            model="gpt-4",
            usage={"total_tokens": 100, "prompt_tokens": 50, "completion_tokens": 50},
            processing_time=1.0,
            metadata={"temperature": 0.7},
        )
        return client

    @pytest.fixture
    def mock_formatter(self):
        """Create mock formatter."""
        return Mock()

    @pytest.fixture
    def mock_ingestion_pipeline(self):
        """Create mock ingestion pipeline."""
        pipeline = AsyncMock()

        # Create a mock embedded chunk and search result
        mock_chunk = Mock()
        mock_chunk.content = "class TestClass:\n    pass"
        mock_chunk.file_metadata.file_path = "src/test.py"

        mock_embedded_chunk = Mock()
        mock_embedded_chunk.chunk = mock_chunk

        mock_search_result = SearchResult(
            embedded_chunk=mock_embedded_chunk,
            similarity_score=0.9,
            rank=1
        )

        pipeline.search_repository.return_value = [mock_search_result]
        return pipeline

    @pytest.fixture
    def agent(self, mock_settings, mock_llm_client, mock_formatter, mock_ingestion_pipeline):
        """Create agent instance."""
        return TechnicalArchitectAgent(
            settings=mock_settings,
            llm_client=mock_llm_client,
            formatter=mock_formatter,
            ingestion_pipeline=mock_ingestion_pipeline,
        )

    @pytest.fixture
    def sample_context(self):
        """Create sample agent context."""
        return AgentContext(
            conversation_context=ConversationContext(session_id="test-session"),
            repository_context={"repository_url": "https://github.com/test/repo"},
        )

    def test_initialization(self, agent):
        """Test agent initialization."""
        assert agent.agent_type == AgentType.TECHNICAL_ARCHITECT
        assert agent.analyzer is not None
        assert agent.designer is not None
        assert agent.validator is not None
        assert agent.standards_manager is not None
        assert agent.max_search_results == 20
        assert agent.min_confidence_threshold == 0.7

    def test_can_handle_query_high_confidence(self, agent, sample_context):
        """Test high confidence query handling."""
        high_confidence_queries = [
            "Design the system architecture",
            "What architectural patterns should we use?",
            "How to refactor this component?",
            "Analyze the SOLID compliance",
            "Create a technical design for the API",
        ]

        for query in high_confidence_queries:
            confidence = agent.can_handle_query(query, sample_context)
            assert confidence >= 0.7, f"Query '{query}' should have high confidence"

    def test_can_handle_query_medium_confidence(self, agent, sample_context):
        """Test medium confidence query handling."""
        medium_confidence_queries = [
            "How to implement this feature?",
            "What are the best practices for this?",
            "Build a service layer",
        ]

        for query in medium_confidence_queries:
            confidence = agent.can_handle_query(query, sample_context)
            assert 0.4 <= confidence < 0.8, f"Query '{query}' should have medium confidence"

    def test_can_handle_query_low_confidence(self, agent, sample_context):
        """Test low confidence query handling."""
        low_confidence_queries = [
            "What is this function?",
            "Find all classes in the codebase",
            "Search for specific code",
            "Create a task plan",
            "Generate a timeline",
        ]

        for query in low_confidence_queries:
            confidence = agent.can_handle_query(query, sample_context)
            assert confidence <= 0.4, f"Query '{query}' should have low confidence"

    @pytest.mark.asyncio
    async def test_process_query_analysis(self, agent, sample_context, mock_ingestion_pipeline):
        """Test processing analysis query."""
        query = "Analyze the current architecture"

        with patch.object(agent.analyzer, "analyze_codebase") as mock_analyze:
            mock_analyze.return_value = ArchitectureAnalysis(
                patterns_detected=[ArchitecturePattern.FACTORY],
                solid_compliance={},
                code_quality_metrics={"total_components": 5},
                confidence=0.8,
            )

            response = await agent.process_query(query, sample_context)

            assert isinstance(response, AgentResponse)
            assert response.agent_type == AgentType.TECHNICAL_ARCHITECT
            assert len(response.content) > 0
            assert response.confidence > 0
            assert response.processing_time > 0
            assert "analysis" in response.metadata["query_type"]

    @pytest.mark.asyncio
    async def test_process_query_design(self, agent, sample_context):
        """Test processing design query."""
        query = "Design a new API component"

        with patch.object(agent.analyzer, "analyze_codebase") as mock_analyze, \
             patch.object(agent.designer, "generate_design") as mock_design:

            mock_analyze.return_value = ArchitectureAnalysis(confidence=0.8)
            mock_design.return_value = Mock(
                design_type=DesignType.API_DESIGN,
                title="API Design",
                overview="Test overview",
                recommendations=[],
                compliance_notes=[],
                confidence=0.9,
            )

            response = await agent.process_query(query, sample_context)

            assert isinstance(response, AgentResponse)
            assert response.agent_type == AgentType.TECHNICAL_ARCHITECT
            assert "design" in response.metadata["query_type"]

    @pytest.mark.asyncio
    async def test_process_query_validation(self, agent, sample_context):
        """Test processing validation query."""
        query = "Validate SOLID compliance"

        with patch.object(agent.analyzer, "analyze_codebase") as mock_analyze, \
             patch.object(agent.validator, "validate_architecture") as mock_validate:

            mock_analyze.return_value = ArchitectureAnalysis(confidence=0.8)
            mock_validate.return_value = Mock(
                overall_score=0.7,
                principle_scores={},
                issues=[],
                recommendations=[],
                compliant_components=[],
                non_compliant_components=[],
                summary={"total_issues": 0},
            )

            response = await agent.process_query(query, sample_context)

            assert isinstance(response, AgentResponse)
            assert response.agent_type == AgentType.TECHNICAL_ARCHITECT
            assert "validation" in response.metadata["query_type"]

    @pytest.mark.asyncio
    async def test_process_query_standards(self, agent, sample_context):
        """Test processing standards query."""
        query = "What are the project standards?"

        with patch.object(agent.standards_manager, "get_standards_summary") as mock_summary:
            mock_summary.return_value = {
                "total_rules": 10,
                "mandatory_rules": 5,
                "recommended_rules": 3,
                "guidance_rules": 2,
                "design_principles": ["SOLID"],
                "technology_stack": {"language": "Python"},
                "coding_standards": {"style": "PEP 8"},
                "testing_requirements": {"framework": "pytest"},
                "architectural_constraints": ["Separation of concerns"],
            }

            response = await agent.process_query(query, sample_context)

            assert isinstance(response, AgentResponse)
            assert response.agent_type == AgentType.TECHNICAL_ARCHITECT
            assert "standards" in response.metadata["query_type"]

    @pytest.mark.asyncio
    async def test_process_query_general(self, agent, sample_context):
        """Test processing general query."""
        query = "How should I structure this module?"

        with patch.object(agent.analyzer, "analyze_codebase") as mock_analyze:
            mock_analyze.return_value = ArchitectureAnalysis(
                patterns_detected=[],
                solid_compliance={},
                code_quality_metrics={},
                confidence=0.7,
            )

            response = await agent.process_query(query, sample_context)

            assert isinstance(response, AgentResponse)
            assert response.agent_type == AgentType.TECHNICAL_ARCHITECT
            assert "general" in response.metadata["query_type"]

    @pytest.mark.asyncio
    async def test_process_query_with_existing_search_results(self, agent, sample_context):
        """Test processing query with existing search results."""
        query = "Analyze this architecture"

        # Add search results to context
        mock_chunk = Mock()
        mock_chunk.content = "class ExistingClass:\n    pass"
        mock_chunk.file_metadata.file_path = "src/existing.py"

        mock_embedded_chunk = Mock()
        mock_embedded_chunk.chunk = mock_chunk

        sample_context.search_results = [
            SearchResult(
                embedded_chunk=mock_embedded_chunk,
                similarity_score=0.8,
                rank=1
            )
        ]

        with patch.object(agent.analyzer, "analyze_codebase") as mock_analyze:
            mock_analyze.return_value = ArchitectureAnalysis(confidence=0.8)

            response = await agent.process_query(query, sample_context)

            assert isinstance(response, AgentResponse)
            # Should use existing search results, not perform new search
            agent.ingestion_pipeline.search_repository.assert_not_called()

    @pytest.mark.asyncio
    async def test_process_query_error_handling(self, agent, sample_context):
        """Test error handling in query processing."""
        query = "Test error handling"

        with patch.object(agent.analyzer, "analyze_codebase") as mock_analyze:
            mock_analyze.side_effect = Exception("Test error")

            with pytest.raises(Exception):
                await agent.process_query(query, sample_context)

    def test_classify_query_type(self, agent):
        """Test query type classification."""
        assert agent._classify_query_type("Analyze the architecture") == "analysis"
        assert agent._classify_query_type("Design a new component") == "design"
        assert agent._classify_query_type("Validate SOLID compliance") == "validation"
        assert agent._classify_query_type("What are the standards?") == "standards"
        assert agent._classify_query_type("How to structure this?") == "general"

    def test_determine_design_type(self, agent):
        """Test design type determination."""
        assert agent._determine_design_type("Create API endpoints") == DesignType.API_DESIGN
        assert agent._determine_design_type("Design new component") == DesignType.COMPONENT_DESIGN
        assert agent._determine_design_type("Refactor this code") == DesignType.REFACTORING_PLAN
        assert agent._determine_design_type("System architecture") == DesignType.ARCHITECTURE_OVERVIEW

    def test_extract_requirements_from_query(self, agent):
        """Test requirement extraction."""
        requirements = agent._extract_requirements_from_query("Create new agent with tests")

        assert isinstance(requirements, list)
        assert len(requirements) > 1
        assert any("agent" in req.lower() for req in requirements)
        assert any("test" in req.lower() for req in requirements)

    def test_extract_sources(self, agent):
        """Test source extraction."""
        def create_search_result(file_path):
            mock_chunk = Mock()
            mock_chunk.file_metadata.file_path = file_path
            mock_embedded_chunk = Mock()
            mock_embedded_chunk.chunk = mock_chunk
            return SearchResult(
                embedded_chunk=mock_embedded_chunk,
                similarity_score=0.9,
                rank=1
            )

        search_results = [
            create_search_result("src/file1.py"),
            create_search_result("src/file2.py"),
        ]

        sources = agent._extract_sources(search_results)

        assert isinstance(sources, list)
        assert len(sources) == 2
        assert "src/file1.py" in sources
        assert "src/file2.py" in sources

    def test_calculate_confidence(self, agent):
        """Test confidence calculation."""
        analysis = Mock()
        analysis.confidence = 0.8
        analysis.patterns_detected = [ArchitecturePattern.FACTORY, ArchitecturePattern.STRATEGY]

        search_results = [Mock() for _ in range(5)]

        confidence = agent._calculate_confidence(analysis, search_results)

        assert isinstance(confidence, float)
        assert 0 <= confidence <= 1

    def test_format_solid_scores(self, agent):
        """Test SOLID scores formatting."""
        from src.technical_architect.analyzer import SOLIDPrinciple

        solid_compliance = {
            SOLIDPrinciple.SINGLE_RESPONSIBILITY: 0.8,
            SOLIDPrinciple.OPEN_CLOSED: 0.6,
        }

        formatted = agent._format_solid_scores(solid_compliance)

        assert isinstance(formatted, str)
        assert "Single Responsibility" in formatted
        assert "80.0%" in formatted  # Should contain the percentage value

    def test_format_list(self, agent):
        """Test list formatting."""
        items = ["Item 1", "Item 2", "Item 3"]
        formatted = agent._format_list(items)

        assert isinstance(formatted, str)
        assert "- Item 1" in formatted
        assert "- Item 2" in formatted

        # Test empty list
        empty_formatted = agent._format_list([])
        assert empty_formatted == "None"

    def test_format_dict(self, agent):
        """Test dictionary formatting."""
        data = {"key1": "value1", "key2": "value2"}
        formatted = agent._format_dict(data)

        assert isinstance(formatted, str)
        assert "- key1: value1" in formatted
        assert "- key2: value2" in formatted

        # Test empty dict
        empty_formatted = agent._format_dict({})
        assert empty_formatted == "None"

    def test_enhance_query_for_search(self, agent):
        """Test query enhancement for search."""
        query = "Design SOLID patterns"
        enhanced = agent._enhance_query_for_search(query)

        assert isinstance(enhanced, str)
        assert "Design SOLID patterns" in enhanced
        # Should add relevant terms
        assert len(enhanced) >= len(query)
