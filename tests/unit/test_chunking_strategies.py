"""
Unit tests for chunking strategies.

Tests the various chunking strategies including text, markdown, code, and config
file chunking with different content types and edge cases.
"""

import pytest
from unittest.mock import Mock
from pathlib import Path

from src.ingestion.strategies import (
    TextChunkingStrategy,
    MarkdownChunkingStrategy,
    CodeChunkingStrategy,
    ConfigChunkingStrategy,
    ChunkingStrategyFactory,
)
from src.ingestion.base import FileMetadata, ChunkType
from src.config import Settings


@pytest.fixture
def mock_settings():
    """Create mock settings for testing."""
    settings = Mock(spec=Settings)
    settings.chunk_size = 500
    settings.chunk_overlap = 100
    settings.max_chunk_size = 1000
    settings.min_chunk_size = 50
    return settings


@pytest.fixture
def sample_file_metadata():
    """Create sample file metadata for testing."""
    from datetime import datetime
    return FileMetadata(
        file_path="test_file.py",
        file_size=1024,
        file_type="text",
        last_modified=datetime.now(),
        language="python",
    )


class TestTextChunkingStrategy:
    """Test cases for TextChunkingStrategy."""
    
    def test_is_applicable_txt_file(self, mock_settings):
        """Test that strategy applies to .txt files."""
        strategy = TextChunkingStrategy(mock_settings)
        metadata = FileMetadata(
            file_path="test.txt",
            file_size=100,
            file_type="text",
            last_modified=None
        )
        assert strategy.is_applicable(metadata) is True
    
    def test_is_applicable_readme_file(self, mock_settings):
        """Test that strategy applies to README files."""
        strategy = TextChunkingStrategy(mock_settings)
        metadata = FileMetadata(
            file_path="README",
            file_size=100,
            file_type="text",
            last_modified=None
        )
        assert strategy.is_applicable(metadata) is True
    
    def test_is_not_applicable_code_file(self, mock_settings):
        """Test that strategy doesn't apply to code files."""
        strategy = TextChunkingStrategy(mock_settings)
        metadata = FileMetadata(
            file_path="test.py",
            file_size=100,
            file_type="text",
            last_modified=None,
            language="python"
        )
        assert strategy.is_applicable(metadata) is False
    
    @pytest.mark.asyncio
    async def test_chunk_simple_text(self, mock_settings, sample_file_metadata):
        """Test chunking simple text content."""
        strategy = TextChunkingStrategy(mock_settings)
        content = "This is a simple paragraph.\n\nThis is another paragraph with more content."
        
        chunks = await strategy.chunk_content(content, sample_file_metadata)
        
        assert len(chunks) >= 1
        assert all(chunk.chunk_type == ChunkType.TEXT_PARAGRAPH for chunk in chunks)
        assert all(chunk.content.strip() for chunk in chunks)
    
    @pytest.mark.asyncio
    async def test_chunk_empty_content(self, mock_settings, sample_file_metadata):
        """Test chunking empty content."""
        strategy = TextChunkingStrategy(mock_settings)
        content = ""
        
        chunks = await strategy.chunk_content(content, sample_file_metadata)
        
        assert len(chunks) == 0


class TestMarkdownChunkingStrategy:
    """Test cases for MarkdownChunkingStrategy."""
    
    def test_is_applicable_md_file(self, mock_settings):
        """Test that strategy applies to .md files."""
        strategy = MarkdownChunkingStrategy(mock_settings)
        metadata = FileMetadata(
            file_path="test.md",
            file_size=100,
            file_type="text",
            last_modified=None
        )
        assert strategy.is_applicable(metadata) is True
    
    def test_is_not_applicable_txt_file(self, mock_settings):
        """Test that strategy doesn't apply to .txt files."""
        strategy = MarkdownChunkingStrategy(mock_settings)
        metadata = FileMetadata(
            file_path="test.txt",
            file_size=100,
            file_type="text",
            last_modified=None
        )
        assert strategy.is_applicable(metadata) is False
    
    @pytest.mark.asyncio
    async def test_chunk_markdown_with_headers(self, mock_settings):
        """Test chunking markdown with headers."""
        strategy = MarkdownChunkingStrategy(mock_settings)
        metadata = FileMetadata(
            file_path="test.md",
            file_size=100,
            file_type="text",
            last_modified=None
        )
        
        content = """# Main Title
This is the introduction.

## Section 1
Content for section 1.

### Subsection 1.1
More detailed content.

## Section 2
Content for section 2.
"""
        
        chunks = await strategy.chunk_content(content, metadata)
        
        assert len(chunks) >= 3  # At least 3 sections
        assert any(chunk.chunk_type == ChunkType.MARKDOWN_SECTION for chunk in chunks)
    
    @pytest.mark.asyncio
    async def test_chunk_markdown_with_code_blocks(self, mock_settings):
        """Test chunking markdown with code blocks."""
        strategy = MarkdownChunkingStrategy(mock_settings)
        metadata = FileMetadata(
            file_path="test.md",
            file_size=100,
            file_type="text",
            last_modified=None
        )
        
        content = """# Code Example

Here's some Python code:

```python
def hello_world():
    print("Hello, World!")
```

And some JavaScript:

```javascript
function helloWorld() {
    console.log("Hello, World!");
}
```
"""
        
        chunks = await strategy.chunk_content(content, metadata)
        
        # Should have section chunks and code block chunks
        section_chunks = [c for c in chunks if c.chunk_type == ChunkType.MARKDOWN_SECTION]
        code_chunks = [c for c in chunks if c.chunk_type == ChunkType.MARKDOWN_CODE_BLOCK]
        
        assert len(section_chunks) >= 1
        assert len(code_chunks) >= 1


class TestCodeChunkingStrategy:
    """Test cases for CodeChunkingStrategy."""
    
    def test_is_applicable_python_file(self, mock_settings):
        """Test that strategy applies to Python files."""
        strategy = CodeChunkingStrategy(mock_settings)
        metadata = FileMetadata(
            file_path="test.py",
            file_size=100,
            file_type="text",
            last_modified=None,
            language="python"
        )
        assert strategy.is_applicable(metadata) is True
    
    def test_is_applicable_by_extension(self, mock_settings):
        """Test that strategy applies based on file extension."""
        strategy = CodeChunkingStrategy(mock_settings)
        metadata = FileMetadata(
            file_path="test.js",
            file_size=100,
            file_type="text",
            last_modified=None
        )
        assert strategy.is_applicable(metadata) is True
    
    @pytest.mark.asyncio
    async def test_chunk_python_code(self, mock_settings):
        """Test chunking Python code."""
        strategy = CodeChunkingStrategy(mock_settings)
        metadata = FileMetadata(
            file_path="test.py",
            file_size=100,
            file_type="text",
            last_modified=None,
            language="python"
        )
        
        content = '''"""Module docstring."""

import os
import sys

class MyClass:
    """Class docstring."""
    
    def __init__(self):
        self.value = 0
    
    def method1(self):
        """Method docstring."""
        return self.value
    
    def method2(self, x):
        """Another method."""
        self.value = x

def standalone_function():
    """Standalone function."""
    return "hello"
'''
        
        chunks = await strategy.chunk_content(content, metadata)
        
        assert len(chunks) >= 1
        # Should have class and function chunks
        chunk_types = {chunk.chunk_type for chunk in chunks}
        assert ChunkType.CODE_CLASS in chunk_types or ChunkType.CODE_FUNCTION in chunk_types


class TestConfigChunkingStrategy:
    """Test cases for ConfigChunkingStrategy."""
    
    def test_is_applicable_json_file(self, mock_settings):
        """Test that strategy applies to JSON files."""
        strategy = ConfigChunkingStrategy(mock_settings)
        metadata = FileMetadata(
            file_path="config.json",
            file_size=100,
            file_type="text",
            last_modified=None
        )
        assert strategy.is_applicable(metadata) is True
    
    def test_is_applicable_yaml_file(self, mock_settings):
        """Test that strategy applies to YAML files."""
        strategy = ConfigChunkingStrategy(mock_settings)
        metadata = FileMetadata(
            file_path="config.yaml",
            file_size=100,
            file_type="text",
            last_modified=None
        )
        assert strategy.is_applicable(metadata) is True
    
    @pytest.mark.asyncio
    async def test_chunk_json_config(self, mock_settings):
        """Test chunking JSON configuration."""
        strategy = ConfigChunkingStrategy(mock_settings)
        metadata = FileMetadata(
            file_path="config.json",
            file_size=100,
            file_type="text",
            last_modified=None
        )
        
        content = '''{
    "database": {
        "host": "localhost",
        "port": 5432,
        "name": "mydb"
    },
    "api": {
        "version": "v1",
        "timeout": 30
    },
    "logging": {
        "level": "INFO",
        "file": "app.log"
    }
}'''
        
        chunks = await strategy.chunk_content(content, metadata)
        
        assert len(chunks) >= 1
        assert all(chunk.chunk_type in [ChunkType.CONFIG_SECTION, ChunkType.CONFIG_KEY_VALUE] for chunk in chunks)


class TestChunkingStrategyFactory:
    """Test cases for ChunkingStrategyFactory."""
    
    def test_get_strategy_for_markdown(self, mock_settings):
        """Test getting strategy for markdown file."""
        factory = ChunkingStrategyFactory(mock_settings)
        metadata = FileMetadata(
            file_path="test.md",
            file_size=100,
            file_type="text",
            last_modified=None
        )
        
        strategy = factory.get_strategy(metadata)
        assert isinstance(strategy, MarkdownChunkingStrategy)
    
    def test_get_strategy_for_python(self, mock_settings):
        """Test getting strategy for Python file."""
        factory = ChunkingStrategyFactory(mock_settings)
        metadata = FileMetadata(
            file_path="test.py",
            file_size=100,
            file_type="text",
            last_modified=None,
            language="python"
        )
        
        strategy = factory.get_strategy(metadata)
        assert isinstance(strategy, CodeChunkingStrategy)
    
    def test_get_strategy_for_json(self, mock_settings):
        """Test getting strategy for JSON file."""
        factory = ChunkingStrategyFactory(mock_settings)
        metadata = FileMetadata(
            file_path="config.json",
            file_size=100,
            file_type="text",
            last_modified=None
        )
        
        strategy = factory.get_strategy(metadata)
        assert isinstance(strategy, ConfigChunkingStrategy)
    
    def test_get_strategy_fallback_to_text(self, mock_settings):
        """Test fallback to text strategy for unknown files."""
        factory = ChunkingStrategyFactory(mock_settings)
        metadata = FileMetadata(
            file_path="unknown.xyz",
            file_size=100,
            file_type="text",
            last_modified=None
        )
        
        strategy = factory.get_strategy(metadata)
        assert isinstance(strategy, TextChunkingStrategy)
    
    def test_get_available_strategies(self, mock_settings):
        """Test getting list of available strategies."""
        factory = ChunkingStrategyFactory(mock_settings)
        strategies = factory.get_available_strategies()
        
        expected_strategies = ["text", "markdown", "code", "config"]
        assert all(strategy in strategies for strategy in expected_strategies)
