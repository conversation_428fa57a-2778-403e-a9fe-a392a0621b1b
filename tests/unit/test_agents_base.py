"""
Unit tests for agent base classes and data structures.
"""

import pytest
from datetime import datetime
from unittest.mock import Mock

from src.agents.base import (
    AgentType,
    MessageRole,
    Message,
    ConversationContext,
    AgentContext,
    AgentResponse,
    Agent,
)
from src.agents.exceptions import AgentTimeoutError


class TestAgentType:
    """Test AgentType enumeration."""

    def test_agent_types_exist(self):
        """Test that all expected agent types exist."""
        assert AgentType.ORCHESTRATOR.value == "orchestrator"
        assert AgentType.TECHNICAL_ARCHITECT.value == "technical_architect"
        assert AgentType.TASK_PLANNER.value == "task_planner"
        assert AgentType.RAG_RETRIEVAL.value == "rag_retrieval"


class TestMessage:
    """Test Message data class."""

    def test_message_creation(self):
        """Test message creation with default values."""
        message = Message(role=MessageRole.USER, content="Test message")
        
        assert message.role == MessageRole.USER
        assert message.content == "Test message"
        assert isinstance(message.timestamp, datetime)
        assert isinstance(message.metadata, dict)
        assert len(message.message_id) > 0

    def test_message_with_metadata(self):
        """Test message creation with custom metadata."""
        metadata = {"source": "test", "priority": "high"}
        message = Message(
            role=MessageRole.ASSISTANT,
            content="Response",
            metadata=metadata
        )
        
        assert message.metadata == metadata


class TestConversationContext:
    """Test ConversationContext data class."""

    def test_context_creation(self):
        """Test conversation context creation."""
        context = ConversationContext(session_id="test-session")
        
        assert context.session_id == "test-session"
        assert context.user_id is None
        assert len(context.conversation_history) == 0
        assert len(context.active_agents) == 0
        assert isinstance(context.shared_state, dict)

    def test_add_message(self):
        """Test adding messages to conversation history."""
        context = ConversationContext(session_id="test")
        message = Message(role=MessageRole.USER, content="Hello")
        
        initial_time = context.last_updated
        context.add_message(message)
        
        assert len(context.conversation_history) == 1
        assert context.conversation_history[0] == message
        assert context.last_updated > initial_time

    def test_get_recent_messages(self):
        """Test getting recent messages."""
        context = ConversationContext(session_id="test")
        
        # Add multiple messages
        for i in range(15):
            message = Message(role=MessageRole.USER, content=f"Message {i}")
            context.add_message(message)
        
        # Test default count (10)
        recent = context.get_recent_messages()
        assert len(recent) == 10
        assert recent[-1].content == "Message 14"
        
        # Test custom count
        recent_5 = context.get_recent_messages(5)
        assert len(recent_5) == 5

    def test_get_context_summary(self):
        """Test getting context summary."""
        context = ConversationContext(session_id="test")
        
        # Empty context
        summary = context.get_context_summary()
        assert summary == "No conversation history"
        
        # With messages
        context.add_message(Message(role=MessageRole.USER, content="Hello"))
        context.add_message(Message(role=MessageRole.ASSISTANT, content="Hi there"))
        
        summary = context.get_context_summary()
        assert "User: Hello" in summary
        assert "Assistant: Hi there" in summary


class TestAgentContext:
    """Test AgentContext data class."""

    def test_context_creation(self):
        """Test agent context creation."""
        conv_context = ConversationContext(session_id="test")
        context = AgentContext(conversation_context=conv_context)
        
        assert context.conversation_context == conv_context
        assert context.repository_context is None
        assert len(context.search_results) == 0
        assert len(context.previous_agent_outputs) == 0

    def test_add_search_results(self):
        """Test adding search results."""
        conv_context = ConversationContext(session_id="test")
        context = AgentContext(conversation_context=conv_context)
        
        # Mock search results
        mock_results = [Mock(), Mock()]
        context.add_search_results(mock_results)
        
        assert len(context.search_results) == 2

    def test_get_relevant_content_empty(self):
        """Test getting relevant content with no results."""
        conv_context = ConversationContext(session_id="test")
        context = AgentContext(conversation_context=conv_context)
        
        content = context.get_relevant_content()
        assert content == "No relevant content found."


class TestAgentResponse:
    """Test AgentResponse data class."""

    def test_response_creation(self):
        """Test agent response creation."""
        response = AgentResponse(
            agent_type=AgentType.RAG_RETRIEVAL,
            content="Test response"
        )
        
        assert response.agent_type == AgentType.RAG_RETRIEVAL
        assert response.content == "Test response"
        assert response.confidence == 1.0
        assert len(response.sources) == 0
        assert isinstance(response.timestamp, datetime)

    def test_add_source(self):
        """Test adding sources to response."""
        response = AgentResponse(
            agent_type=AgentType.RAG_RETRIEVAL,
            content="Test"
        )
        
        response.add_source("file1.py")
        response.add_source("file2.py")
        response.add_source("file1.py")  # Duplicate
        
        assert len(response.sources) == 2
        assert "file1.py" in response.sources
        assert "file2.py" in response.sources

    def test_format_sources(self):
        """Test formatting sources for display."""
        response = AgentResponse(
            agent_type=AgentType.RAG_RETRIEVAL,
            content="Test"
        )
        
        # No sources
        formatted = response.format_sources()
        assert formatted == ""
        
        # With sources
        response.add_source("file1.py")
        response.add_source("file2.py")
        
        formatted = response.format_sources()
        assert "**Sources:**" in formatted
        assert "- file1.py" in formatted
        assert "- file2.py" in formatted


class TestAgent:
    """Test Agent abstract base class."""

    def test_agent_initialization(self):
        """Test agent initialization."""
        # Create a concrete implementation for testing
        class TestAgent(Agent):
            async def process_query(self, query: str, context: AgentContext) -> AgentResponse:
                return AgentResponse(
                    agent_type=self.agent_type,
                    content="Test response"
                )
            
            def can_handle_query(self, query: str, context: AgentContext) -> float:
                return 0.8
        
        settings = Mock()
        agent = TestAgent(AgentType.RAG_RETRIEVAL, settings)
        
        assert agent.agent_type == AgentType.RAG_RETRIEVAL
        assert agent.settings == settings
        assert agent._stats["total_requests"] == 0

    def test_update_stats(self):
        """Test statistics updating."""
        class TestAgent(Agent):
            async def process_query(self, query: str, context: AgentContext) -> AgentResponse:
                return AgentResponse(agent_type=self.agent_type, content="Test")
            
            def can_handle_query(self, query: str, context: AgentContext) -> float:
                return 0.8
        
        agent = TestAgent(AgentType.RAG_RETRIEVAL, Mock())
        
        # Test successful request
        agent._update_stats(1.5, success=True)
        stats = agent.get_stats()
        
        assert stats["total_requests"] == 1
        assert stats["successful_requests"] == 1
        assert stats["failed_requests"] == 0
        assert stats["average_processing_time"] == 1.5

    def test_reset_stats(self):
        """Test resetting statistics."""
        class TestAgent(Agent):
            async def process_query(self, query: str, context: AgentContext) -> AgentResponse:
                return AgentResponse(agent_type=self.agent_type, content="Test")
            
            def can_handle_query(self, query: str, context: AgentContext) -> float:
                return 0.8
        
        agent = TestAgent(AgentType.RAG_RETRIEVAL, Mock())
        
        # Add some stats
        agent._update_stats(1.0, success=True)
        assert agent.get_stats()["total_requests"] == 1
        
        # Reset
        agent.reset_stats()
        assert agent.get_stats()["total_requests"] == 0

    def test_string_representation(self):
        """Test string representation of agent."""
        class TestAgent(Agent):
            async def process_query(self, query: str, context: AgentContext) -> AgentResponse:
                return AgentResponse(agent_type=self.agent_type, content="Test")
            
            def can_handle_query(self, query: str, context: AgentContext) -> float:
                return 0.8
        
        agent = TestAgent(AgentType.RAG_RETRIEVAL, Mock())
        
        str_repr = str(agent)
        assert "TestAgent" in str_repr
        assert "rag_retrieval" in str_repr
        
        assert repr(agent) == str(agent)
