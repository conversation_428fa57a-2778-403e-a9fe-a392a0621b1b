"""
Unit tests for query classifier.
"""

import pytest

from src.orchestrator.classifier import QueryClassifier, QueryIntent, ClassificationResult
from src.agents.base import AgentType


class TestQueryClassifier:
    """Test QueryClassifier functionality."""

    def setup_method(self):
        """Set up test fixtures."""
        self.classifier = QueryClassifier()

    def test_classifier_initialization(self):
        """Test classifier initialization."""
        assert self.classifier is not None
        assert len(self.classifier.intent_patterns) > 0

    def test_architecture_design_classification(self):
        """Test classification of architecture design queries."""
        queries = [
            "How should I design the authentication system?",
            "What architecture pattern should I use?",
            "Design a microservice system",
            "What's the best database design approach?"
        ]
        
        for query in queries:
            result = self.classifier.classify_query(query)
            assert result.intent == QueryIntent.ARCHITECTURE_DESIGN
            assert result.agent_type == AgentType.TECHNICAL_ARCHITECT
            assert result.confidence > 0.0

    def test_task_planning_classification(self):
        """Test classification of task planning queries."""
        queries = [
            "Create a plan to implement user management",
            "How do I build this feature step by step?",
            "Break down this requirement into tasks",
            "What's the implementation roadmap?"
        ]
        
        for query in queries:
            result = self.classifier.classify_query(query)
            assert result.intent == QueryIntent.TASK_PLANNING
            assert result.agent_type == AgentType.TASK_PLANNER
            assert result.confidence > 0.0

    def test_code_lookup_classification(self):
        """Test classification of code lookup queries."""
        queries = [
            "Show me the login function",
            "Find the User class implementation",
            "Where is the authentication method?",
            "What does this function do?"
        ]
        
        for query in queries:
            result = self.classifier.classify_query(query)
            assert result.intent == QueryIntent.CODE_LOOKUP
            assert result.agent_type == AgentType.RAG_RETRIEVAL
            assert result.confidence > 0.0

    def test_factual_query_classification(self):
        """Test classification of factual queries."""
        queries = [
            "What is OAuth?",
            "Explain dependency injection",
            "Define microservices",
            "Tell me about REST APIs"
        ]
        
        for query in queries:
            result = self.classifier.classify_query(query)
            assert result.intent == QueryIntent.FACTUAL_QUERY
            assert result.agent_type == AgentType.RAG_RETRIEVAL
            assert result.confidence > 0.0

    def test_documentation_classification(self):
        """Test classification of documentation queries."""
        queries = [
            "documentation for this API",
            "user guide for authentication",
            "tutorial on setting up the system",
            "reference for this library"
        ]

        for query in queries:
            result = self.classifier.classify_query(query)
            assert result.intent == QueryIntent.DOCUMENTATION
            assert result.agent_type == AgentType.RAG_RETRIEVAL
            assert result.confidence > 0.0

    def test_confidence_adjustments(self):
        """Test confidence score adjustments."""
        # Question mark should boost confidence
        result1 = self.classifier.classify_query("What is OAuth?")
        result2 = self.classifier.classify_query("What is OAuth")
        assert result1.confidence >= result2.confidence

        # Technical terms should boost confidence
        result3 = self.classifier.classify_query("Show me the API function")
        result4 = self.classifier.classify_query("Show me the function")
        assert result3.confidence >= result4.confidence

    def test_short_query_penalty(self):
        """Test that very short queries get confidence penalty."""
        short_result = self.classifier.classify_query("Help")
        longer_result = self.classifier.classify_query("Help me understand OAuth")
        
        # Longer query should have higher confidence
        assert longer_result.confidence > short_result.confidence

    def test_imperative_statements_boost(self):
        """Test that imperative statements get confidence boost."""
        imperative_result = self.classifier.classify_query("Show me the login function")
        non_imperative_result = self.classifier.classify_query("I need the login function")
        
        # Imperative should have higher confidence
        assert imperative_result.confidence >= non_imperative_result.confidence

    def test_classification_result_structure(self):
        """Test that classification result has proper structure."""
        result = self.classifier.classify_query("How should I design the API?")
        
        assert isinstance(result, ClassificationResult)
        assert isinstance(result.intent, QueryIntent)
        assert isinstance(result.agent_type, AgentType)
        assert isinstance(result.confidence, float)
        assert isinstance(result.reasoning, str)
        assert isinstance(result.keywords_matched, list)
        
        assert 0.0 <= result.confidence <= 1.0
        assert len(result.reasoning) > 0

    def test_keywords_matching(self):
        """Test that keywords are properly matched and recorded."""
        result = self.classifier.classify_query("Design a microservice architecture")
        
        # Should match architecture-related keywords
        assert len(result.keywords_matched) > 0
        # Should contain some architecture-related terms
        matched_text = " ".join(result.keywords_matched).lower()
        assert any(term in matched_text for term in ["design", "architecture", "microservice"])

    def test_get_classification_stats(self):
        """Test getting classification statistics."""
        stats = self.classifier.get_classification_stats()
        
        assert isinstance(stats, dict)
        assert len(stats) > 0
        
        # Check that all intents are represented
        for intent in QueryIntent:
            assert intent.value in stats
            
            intent_stats = stats[intent.value]
            assert "keywords_count" in intent_stats
            assert "phrases_count" in intent_stats
            assert "agent_type" in intent_stats
            assert intent_stats["keywords_count"] > 0

    def test_edge_cases(self):
        """Test edge cases and unusual inputs."""
        # Empty query
        result = self.classifier.classify_query("")
        assert isinstance(result, ClassificationResult)
        assert result.confidence < 0.5  # Should have low confidence
        
        # Very long query
        long_query = "This is a very long query " * 20 + "about architecture design"
        result = self.classifier.classify_query(long_query)
        assert isinstance(result, ClassificationResult)
        
        # Special characters
        result = self.classifier.classify_query("What is @#$%^&*() design?")
        assert isinstance(result, ClassificationResult)

    def test_confidence_bounds(self):
        """Test that confidence scores are properly bounded."""
        test_queries = [
            "How should I design the authentication system?",
            "Create a plan to implement user management",
            "Show me the login function",
            "What is OAuth?",
            "Help",
            "",
            "This is a very specific technical query about microservice architecture design patterns"
        ]
        
        for query in test_queries:
            result = self.classifier.classify_query(query)
            assert 0.0 <= result.confidence <= 1.0, f"Confidence out of bounds for query: {query}"

    def test_reasoning_generation(self):
        """Test that reasoning is properly generated."""
        result = self.classifier.classify_query("Design a REST API architecture")
        
        assert len(result.reasoning) > 0
        assert "Architecture" in result.reasoning or "architecture" in result.reasoning
        
        # Should mention confidence level
        if result.confidence > 0.8:
            assert "High confidence" in result.reasoning
        elif result.confidence > 0.6:
            assert "Medium confidence" in result.reasoning
        else:
            assert "Low confidence" in result.reasoning
