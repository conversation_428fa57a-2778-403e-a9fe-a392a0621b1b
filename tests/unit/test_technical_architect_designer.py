"""
Unit tests for Technical Architect Designer module.
"""

import pytest
from unittest.mock import Mock

from src.technical_architect.analyzer import ArchitectureAnalysis, ArchitecturePattern, SOLIDPrinciple
from src.technical_architect.designer import (
    DesignDocument,
    DesignPriority,
    DesignRecommendation,
    DesignType,
    TechnicalDesigner,
)


class TestTechnicalDesigner:
    """Test cases for TechnicalDesigner."""

    @pytest.fixture
    def designer(self):
        """Create designer instance."""
        return TechnicalDesigner()

    @pytest.fixture
    def sample_analysis(self):
        """Create sample architecture analysis."""
        return ArchitectureAnalysis(
            patterns_detected=[ArchitecturePattern.FACTORY, ArchitecturePattern.REPOSITORY],
            solid_compliance={
                SOLIDPrinciple.SINGLE_RESPONSIBILITY: 0.8,
                S<PERSON><PERSON>Principle.OPEN_CLOSED: 0.6,
                SOLIDPrinciple.LISKOV_SUBSTITUTION: 0.9,
                SOLIDPrinciple.INTERFACE_SEGREGATION: 0.7,
                SOLIDPrinciple.DEPENDENCY_INVERSION: 0.5,
            },
            code_quality_metrics={
                "total_components": 10,
                "average_complexity": 4.5,
                "max_complexity": 8.0,
                "components_with_violations": 3,
            },
            recommendations=["Improve dependency injection", "Reduce complexity"],
            concerns=["High complexity in some components"],
            strengths=["Good use of factory pattern"],
            confidence=0.8,
        )

    @pytest.fixture
    def sample_project_standards(self):
        """Create sample project standards."""
        return {
            "coding_standards": {"style_guide": "PEP 8", "formatter": "Black"},
            "testing_requirements": {"framework": "pytest", "min_coverage": 80},
            "design_principles": ["Follow SOLID principles", "Use dependency injection"],
        }

    def test_generate_architecture_overview(self, designer, sample_analysis, sample_project_standards):
        """Test architecture overview generation."""
        requirements = ["Design system architecture", "Ensure scalability"]

        design = designer.generate_design(
            design_type=DesignType.ARCHITECTURE_OVERVIEW,
            requirements=requirements,
            current_analysis=sample_analysis,
            project_standards=sample_project_standards,
        )

        assert isinstance(design, DesignDocument)
        assert design.design_type == DesignType.ARCHITECTURE_OVERVIEW
        assert design.title == "System Architecture Overview"
        assert len(design.recommendations) > 0
        assert len(design.architecture_diagrams) > 0
        assert 0 <= design.confidence <= 1

    def test_generate_component_design(self, designer, sample_analysis, sample_project_standards):
        """Test component design generation."""
        requirements = ["Create new agent component", "Follow existing patterns"]

        design = designer.generate_design(
            design_type=DesignType.COMPONENT_DESIGN,
            requirements=requirements,
            current_analysis=sample_analysis,
            project_standards=sample_project_standards,
        )

        assert isinstance(design, DesignDocument)
        assert design.design_type == DesignType.COMPONENT_DESIGN
        assert design.title == "Component Design Specification"
        assert len(design.recommendations) > 0

    def test_generate_api_design(self, designer, sample_analysis, sample_project_standards):
        """Test API design generation."""
        requirements = ["Create REST API", "Support query processing"]

        design = designer.generate_design(
            design_type=DesignType.API_DESIGN,
            requirements=requirements,
            current_analysis=sample_analysis,
            project_standards=sample_project_standards,
        )

        assert isinstance(design, DesignDocument)
        assert design.design_type == DesignType.API_DESIGN
        assert design.title == "API Design Specification"
        assert len(design.recommendations) >= 2  # Should have standard API recommendations

    def test_generate_refactoring_plan(self, designer, sample_analysis, sample_project_standards):
        """Test refactoring plan generation."""
        # Modify analysis to have high complexity
        sample_analysis.code_quality_metrics["max_complexity"] = 9.0
        sample_analysis.code_quality_metrics["components_with_violations"] = 5

        requirements = ["Improve code quality", "Reduce complexity"]

        design = designer.generate_design(
            design_type=DesignType.REFACTORING_PLAN,
            requirements=requirements,
            current_analysis=sample_analysis,
            project_standards=sample_project_standards,
        )

        assert isinstance(design, DesignDocument)
        assert design.design_type == DesignType.REFACTORING_PLAN
        assert design.title == "Code Refactoring Plan"
        assert len(design.recommendations) > 0
        # Should recommend complexity reduction
        assert any("complexity" in rec.title.lower() for rec in design.recommendations)

    def test_create_pattern_recommendation(self, designer):
        """Test pattern recommendation creation."""
        requirements = ["Improve testability"]

        # Test dependency injection recommendation
        rec = designer._create_pattern_recommendation(
            ArchitecturePattern.DEPENDENCY_INJECTION, requirements
        )

        assert isinstance(rec, DesignRecommendation)
        assert "Dependency Injection" in rec.title
        assert rec.priority == DesignPriority.HIGH
        assert len(rec.implementation_steps) > 0
        assert ArchitecturePattern.DEPENDENCY_INJECTION in rec.patterns_to_apply

        # Test factory pattern recommendation
        rec = designer._create_pattern_recommendation(ArchitecturePattern.FACTORY, requirements)

        assert isinstance(rec, DesignRecommendation)
        assert "Factory" in rec.title
        assert rec.priority == DesignPriority.MEDIUM
        assert len(rec.implementation_steps) > 0

    def test_create_solid_improvement_recommendation(self, designer):
        """Test SOLID improvement recommendation creation."""
        rec = designer._create_solid_improvement_recommendation(
            SOLIDPrinciple.SINGLE_RESPONSIBILITY, 0.4
        )

        assert isinstance(rec, DesignRecommendation)
        assert "Single Responsibility" in rec.title
        assert rec.priority == DesignPriority.HIGH  # Low score should be high priority
        assert len(rec.implementation_steps) > 0

        # Test medium priority for moderate score
        rec = designer._create_solid_improvement_recommendation(
            SOLIDPrinciple.OPEN_CLOSED, 0.6
        )
        assert rec.priority == DesignPriority.MEDIUM

    def test_create_layered_architecture_recommendation(self, designer):
        """Test layered architecture recommendation creation."""
        rec = designer._create_layered_architecture_recommendation()

        assert isinstance(rec, DesignRecommendation)
        assert "Layered Architecture" in rec.title
        assert rec.priority == DesignPriority.HIGH
        assert len(rec.implementation_steps) > 0
        assert len(rec.benefits) > 0
        assert ArchitecturePattern.LAYERED in rec.patterns_to_apply

    def test_create_agent_component_recommendation(self, designer):
        """Test agent component recommendation creation."""
        requirement = "Create new agent for task planning"

        rec = designer._create_agent_component_recommendation(requirement)

        assert isinstance(rec, DesignRecommendation)
        assert "Agent Component" in rec.title
        assert rec.priority == DesignPriority.HIGH
        assert len(rec.implementation_steps) > 0
        assert ArchitecturePattern.STRATEGY in rec.patterns_to_apply
        assert ArchitecturePattern.DEPENDENCY_INJECTION in rec.patterns_to_apply

    def test_create_pipeline_component_recommendation(self, designer):
        """Test pipeline component recommendation creation."""
        requirement = "Create data processing pipeline"

        rec = designer._create_pipeline_component_recommendation(requirement)

        assert isinstance(rec, DesignRecommendation)
        assert "Pipeline Component" in rec.title
        assert rec.priority == DesignPriority.HIGH
        assert len(rec.implementation_steps) > 0
        assert ArchitecturePattern.RAG_PIPELINE in rec.patterns_to_apply

    def test_create_api_component_recommendation(self, designer):
        """Test API component recommendation creation."""
        requirement = "Create REST API endpoints"

        rec = designer._create_api_component_recommendation(requirement)

        assert isinstance(rec, DesignRecommendation)
        assert "API Component" in rec.title
        assert rec.priority == DesignPriority.HIGH
        assert len(rec.implementation_steps) > 0
        assert ArchitecturePattern.FACTORY in rec.patterns_to_apply

    def test_generate_architecture_diagrams(self, designer, sample_analysis):
        """Test architecture diagram generation."""
        diagrams = designer._generate_architecture_diagrams(sample_analysis)

        assert isinstance(diagrams, list)
        assert len(diagrams) >= 2  # Should have system and agent diagrams
        assert all(isinstance(diagram, str) for diagram in diagrams)
        assert any("System Architecture" in diagram for diagram in diagrams)
        assert any("Agent Interaction" in diagram for diagram in diagrams)

    def test_generate_compliance_notes(self, designer, sample_project_standards):
        """Test compliance notes generation."""
        design = DesignDocument(
            design_type=DesignType.ARCHITECTURE_OVERVIEW,
            title="Test Design",
            overview="Test overview",
            recommendations=[
                DesignRecommendation(
                    title="Test Recommendation",
                    description="Test description",
                    priority=DesignPriority.HIGH,
                    rationale="Test rationale",
                    patterns_to_apply=[ArchitecturePattern.DEPENDENCY_INJECTION, ArchitecturePattern.FACTORY],
                )
            ],
        )

        notes = designer._generate_compliance_notes(design, sample_project_standards)

        assert isinstance(notes, list)
        assert len(notes) > 0
        assert any("SOLID" in note for note in notes)
        assert any("test" in note.lower() for note in notes)
        assert any("documentation" in note.lower() for note in notes)

    def test_calculate_design_confidence(self, designer, sample_analysis):
        """Test design confidence calculation."""
        design = DesignDocument(
            design_type=DesignType.ARCHITECTURE_OVERVIEW,
            title="Test Design",
            overview="Test overview",
            recommendations=[
                DesignRecommendation(
                    title="High Priority Rec",
                    description="Test",
                    priority=DesignPriority.HIGH,
                    rationale="Test",
                ),
                DesignRecommendation(
                    title="Medium Priority Rec",
                    description="Test",
                    priority=DesignPriority.MEDIUM,
                    rationale="Test",
                ),
            ],
        )

        confidence = designer._calculate_design_confidence(design, sample_analysis)

        assert isinstance(confidence, float)
        assert 0 <= confidence <= 1

    def test_unsupported_design_type(self, designer, sample_analysis, sample_project_standards):
        """Test handling of unsupported design type."""
        # Create a mock design type that's not in the templates
        with pytest.raises(ValueError, match="Unsupported design type"):
            designer.design_templates = {}  # Clear templates to force error
            designer.generate_design(
                design_type=DesignType.ARCHITECTURE_OVERVIEW,
                requirements=["test"],
                current_analysis=sample_analysis,
                project_standards=sample_project_standards,
            )

    def test_design_recommendation_creation(self):
        """Test DesignRecommendation creation and methods."""
        rec = DesignRecommendation(
            title="Test Recommendation",
            description="Test description",
            priority=DesignPriority.HIGH,
            rationale="Test rationale",
            implementation_steps=["Step 1", "Step 2"],
            affected_components=["Component A"],
            patterns_to_apply=[ArchitecturePattern.FACTORY],
            estimated_effort="2 days",
            risks=["Risk 1"],
            benefits=["Benefit 1"],
        )

        assert rec.title == "Test Recommendation"
        assert rec.priority == DesignPriority.HIGH
        assert len(rec.implementation_steps) == 2
        assert len(rec.patterns_to_apply) == 1
        assert rec.estimated_effort == "2 days"

    def test_design_document_creation(self):
        """Test DesignDocument creation and methods."""
        doc = DesignDocument(
            design_type=DesignType.COMPONENT_DESIGN,
            title="Test Design",
            overview="Test overview",
            recommendations=[],
            architecture_diagrams=["Diagram 1"],
            implementation_timeline={"phase1": "1 week"},
            compliance_notes=["Note 1"],
            references=["Ref 1"],
            confidence=0.8,
        )

        assert doc.design_type == DesignType.COMPONENT_DESIGN
        assert doc.title == "Test Design"
        assert doc.confidence == 0.8
        assert len(doc.architecture_diagrams) == 1
        assert "phase1" in doc.implementation_timeline
