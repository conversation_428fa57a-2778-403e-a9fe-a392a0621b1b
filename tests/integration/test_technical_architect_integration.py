"""
Integration tests for Technical Architect Agent with Orchestrator.
"""

import pytest
from unittest.mock import AsyncMock, Mock, patch

from src.agents.base import Agent<PERSON>ontext, AgentType, ConversationContext
from src.agents.factory import AgentFactory
from src.config import Settings
from src.orchestrator.orchestrator import OrchestratorAgent
from src.technical_architect.agent import TechnicalArchitectAgent


class TestTechnicalArchitectIntegration:
    """Integration tests for Technical Architect Agent."""

    @pytest.fixture
    def mock_settings(self):
        """Create mock settings."""
        settings = Mock(spec=Settings)
        settings.openai_api_key = "test-key"
        settings.openai_model = "gpt-4"
        settings.openai_temperature = 0.7
        settings.openai_max_tokens = 2000
        settings.docs_path = "docs"
        return settings

    @pytest.fixture
    def agent_factory(self, mock_settings):
        """Create agent factory."""
        with patch("src.agents.factory.LLMClientFactory.create_client"), \
             patch("src.agents.factory.IntegratedIngestionPipeline"):
            factory = AgentFactory(mock_settings)
            return factory

    @pytest.fixture
    def orchestrator(self, agent_factory, mock_settings):
        """Create orchestrator agent."""
        with patch("src.agents.factory.LLMClientFactory.create_client") as mock_llm_factory, \
             patch("src.agents.factory.IntegratedIngestionPipeline"):
            
            mock_llm_client = AsyncMock()
            mock_llm_factory.return_value = mock_llm_client
            
            return agent_factory.create_agent(AgentType.ORCHESTRATOR)

    @pytest.fixture
    def technical_architect(self, agent_factory):
        """Create technical architect agent."""
        with patch("src.technical_architect.agent.TechnicalArchitectAgent.__init__", return_value=None):
            agent = agent_factory.create_agent(AgentType.TECHNICAL_ARCHITECT)
            # Mock the agent methods for testing
            agent.agent_type = AgentType.TECHNICAL_ARCHITECT
            agent.can_handle_query = Mock(return_value=0.9)
            agent.process_query = AsyncMock()
            return agent

    @pytest.mark.asyncio
    async def test_orchestrator_routes_to_technical_architect(self, orchestrator, agent_factory):
        """Test that orchestrator correctly routes architecture queries to technical architect."""
        architecture_query = "Design the system architecture for the new feature"
        
        context = AgentContext(
            conversation_context=ConversationContext(session_id="test-session")
        )

        with patch.object(orchestrator, "_get_agent") as mock_get_agent:
            mock_agent = AsyncMock()
            mock_agent.agent_type = AgentType.TECHNICAL_ARCHITECT
            mock_agent.can_handle_query.return_value = 0.9
            mock_agent.process_query.return_value = Mock(
                agent_type=AgentType.TECHNICAL_ARCHITECT,
                content="Architecture design response",
                confidence=0.9,
                sources=[],
                processing_time=1.0,
                metadata={"query_type": "design"},
            )
            mock_get_agent.return_value = mock_agent

            # Mock the routing decision
            with patch.object(orchestrator.router, "route_query") as mock_route:
                mock_route.return_value = Mock(
                    primary_agent=AgentType.TECHNICAL_ARCHITECT,
                    secondary_agents=[],
                    routing_strategy="single_agent",
                    confidence=0.9,
                )

                response = await orchestrator.process_query(architecture_query, context)

                # Verify routing occurred
                mock_route.assert_called_once()
                mock_get_agent.assert_called_with(AgentType.TECHNICAL_ARCHITECT)
                mock_agent.process_query.assert_called_once()

    @pytest.mark.asyncio
    async def test_technical_architect_agent_creation(self, agent_factory):
        """Test that agent factory can create technical architect agent."""
        with patch("src.technical_architect.agent.TechnicalArchitectAgent") as mock_agent_class:
            mock_agent = Mock()
            mock_agent.agent_type = AgentType.TECHNICAL_ARCHITECT
            mock_agent_class.return_value = mock_agent

            agent = agent_factory.create_agent(AgentType.TECHNICAL_ARCHITECT)

            assert agent is not None
            mock_agent_class.assert_called_once()

    def test_agent_factory_has_technical_architect_info(self, agent_factory):
        """Test that agent factory has information about technical architect."""
        available_agents = agent_factory.get_available_agents()

        assert AgentType.TECHNICAL_ARCHITECT in available_agents
        
        tech_architect_info = available_agents[AgentType.TECHNICAL_ARCHITECT]
        assert tech_architect_info["name"] == "Technical Design Architect"
        assert "architecture" in tech_architect_info["description"].lower()
        assert "Architecture analysis" in tech_architect_info["capabilities"]
        assert "Design generation" in tech_architect_info["capabilities"]

    @pytest.mark.asyncio
    async def test_multi_agent_collaboration(self, orchestrator, agent_factory):
        """Test multi-agent collaboration involving technical architect."""
        complex_query = "Design and plan implementation of a new microservice"
        
        context = AgentContext(
            conversation_context=ConversationContext(session_id="test-session")
        )

        with patch.object(orchestrator, "_get_agent") as mock_get_agent:
            # Mock technical architect agent
            mock_tech_agent = AsyncMock()
            mock_tech_agent.agent_type = AgentType.TECHNICAL_ARCHITECT
            mock_tech_agent.can_handle_query.return_value = 0.8
            mock_tech_agent.process_query.return_value = Mock(
                agent_type=AgentType.TECHNICAL_ARCHITECT,
                content="Technical architecture design",
                confidence=0.8,
                sources=[],
                processing_time=1.0,
                metadata={"query_type": "design"},
            )

            # Mock task planner agent
            mock_task_agent = AsyncMock()
            mock_task_agent.agent_type = AgentType.TASK_PLANNER
            mock_task_agent.can_handle_query.return_value = 0.7
            mock_task_agent.process_query.return_value = Mock(
                agent_type=AgentType.TASK_PLANNER,
                content="Implementation plan",
                confidence=0.7,
                sources=[],
                processing_time=1.0,
                metadata={"query_type": "planning"},
            )

            def get_agent_side_effect(agent_type):
                if agent_type == AgentType.TECHNICAL_ARCHITECT:
                    return mock_tech_agent
                elif agent_type == AgentType.TASK_PLANNER:
                    return mock_task_agent
                else:
                    return Mock()

            mock_get_agent.side_effect = get_agent_side_effect

            # Mock multi-agent routing
            with patch.object(orchestrator.router, "route_query") as mock_route:
                mock_route.return_value = Mock(
                    primary_agent=AgentType.TECHNICAL_ARCHITECT,
                    secondary_agents=[AgentType.TASK_PLANNER],
                    routing_strategy="multi_agent",
                    confidence=0.8,
                )

                response = await orchestrator.process_query(complex_query, context)

                # Verify both agents were called
                mock_route.assert_called_once()
                assert mock_get_agent.call_count >= 1

    def test_technical_architect_routing_confidence(self, agent_factory):
        """Test technical architect routing confidence for different query types."""
        with patch("src.technical_architect.agent.TechnicalArchitectAgent") as mock_agent_class:
            mock_agent = TechnicalArchitectAgent.__new__(TechnicalArchitectAgent)
            mock_agent_class.return_value = mock_agent

            # Initialize the agent's can_handle_query method
            TechnicalArchitectAgent.__init__(mock_agent, Mock(), Mock(), Mock(), Mock())

            context = AgentContext(
                conversation_context=ConversationContext(session_id="test-session")
            )

            # Test high confidence queries
            high_confidence_queries = [
                "Design the system architecture",
                "What architectural patterns should we use?",
                "Analyze SOLID compliance",
                "Create technical design document",
            ]

            for query in high_confidence_queries:
                confidence = mock_agent.can_handle_query(query, context)
                assert confidence >= 0.7, f"Query '{query}' should have high confidence"

            # Test low confidence queries (should go to other agents)
            low_confidence_queries = [
                "What is this function?",
                "Create a task timeline",
                "Find all classes",
                "Search for specific code",
            ]

            for query in low_confidence_queries:
                confidence = mock_agent.can_handle_query(query, context)
                assert confidence <= 0.4, f"Query '{query}' should have low confidence"

    @pytest.mark.asyncio
    async def test_technical_architect_error_handling_integration(self, orchestrator, agent_factory):
        """Test error handling when technical architect fails."""
        architecture_query = "Design the system architecture"
        
        context = AgentContext(
            conversation_context=ConversationContext(session_id="test-session")
        )

        with patch.object(orchestrator, "_get_agent") as mock_get_agent:
            mock_agent = AsyncMock()
            mock_agent.agent_type = AgentType.TECHNICAL_ARCHITECT
            mock_agent.can_handle_query.return_value = 0.9
            mock_agent.process_query.side_effect = Exception("Agent processing error")
            mock_get_agent.return_value = mock_agent

            with patch.object(orchestrator.router, "route_query") as mock_route:
                mock_route.return_value = Mock(
                    primary_agent=AgentType.TECHNICAL_ARCHITECT,
                    secondary_agents=[],
                    routing_strategy="single_agent",
                    confidence=0.9,
                )

                # Should handle the error gracefully
                try:
                    response = await orchestrator.process_query(architecture_query, context)
                    # If no exception is raised, the orchestrator handled the error
                    assert response is not None
                except Exception:
                    # If an exception is raised, that's also acceptable for this test
                    # as it shows the error was propagated properly
                    pass

    def test_technical_architect_agent_registry(self, agent_factory):
        """Test that technical architect is properly registered in agent registry."""
        registry = agent_factory.get_available_agents()

        assert AgentType.TECHNICAL_ARCHITECT in registry
        
        tech_architect_config = registry[AgentType.TECHNICAL_ARCHITECT]
        
        # Verify configuration
        assert "name" in tech_architect_config
        assert "description" in tech_architect_config
        assert "capabilities" in tech_architect_config
        
        # Verify capabilities
        capabilities = tech_architect_config["capabilities"]
        expected_capabilities = [
            "Architecture analysis",
            "Design generation", 
            "Standards compliance",
            "Pattern recognition",
        ]
        
        for capability in expected_capabilities:
            assert capability in capabilities

    @pytest.mark.asyncio
    async def test_technical_architect_with_search_results(self, agent_factory):
        """Test technical architect processing with search results."""
        with patch("src.technical_architect.agent.TechnicalArchitectAgent") as mock_agent_class:
            mock_agent = AsyncMock()
            mock_agent.agent_type = AgentType.TECHNICAL_ARCHITECT
            mock_agent_class.return_value = mock_agent

            # Create context with search results
            context = AgentContext(
                conversation_context=ConversationContext(session_id="test-session"),
                search_results=[
                    Mock(
                        content="class TestClass:\n    pass",
                        file_path="src/test.py",
                        similarity_score=0.9,
                    )
                ],
            )

            agent = agent_factory.create_agent(AgentType.TECHNICAL_ARCHITECT)
            
            # Mock the process_query method
            mock_agent.process_query.return_value = Mock(
                agent_type=AgentType.TECHNICAL_ARCHITECT,
                content="Analysis based on search results",
                confidence=0.8,
                sources=["src/test.py"],
                metadata={"search_results_count": 1},
            )

            query = "Analyze the architecture of this code"
            response = await mock_agent.process_query(query, context)

            # Verify the agent was called with search results
            mock_agent.process_query.assert_called_once_with(query, context)
            assert response.metadata["search_results_count"] == 1
