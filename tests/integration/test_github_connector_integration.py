"""
Integration tests for GitHubConnector.

Tests the complete GitHub connector workflow including repository cloning,
file processing, metadata extraction, and error handling.
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime

from src.ingestion.github.connector import GitHubConnector
from src.ingestion.github.client import GitHubClient
from src.ingestion.github.repository import GitHubRepositoryManager
from src.ingestion.github.filters import GitHubFileFilter
from src.ingestion.github.metadata import GitHubMetadataExtractor
from src.ingestion.exceptions import IngestionError
from src.config import Settings


@pytest.fixture
def mock_settings():
    """Create mock settings for testing."""
    settings = Mock(spec=Settings)
    settings.github_token = "test_token"
    settings.github_api_url = "https://api.github.com"
    settings.max_file_size = 1024 * 1024  # 1MB
    settings.allowed_file_types = ["py", "js", "md", "txt", "json"]
    settings.temp_dir = tempfile.mkdtemp()
    return settings


@pytest.fixture
def temp_repo_dir():
    """Create a temporary directory for test repository."""
    temp_dir = Path(tempfile.mkdtemp())
    
    # Create a mock repository structure
    (temp_dir / "src").mkdir()
    (temp_dir / "docs").mkdir()
    (temp_dir / "tests").mkdir()
    
    # Create test files
    (temp_dir / "README.md").write_text("# Test Repository\nThis is a test.")
    (temp_dir / "src" / "main.py").write_text("print('Hello, World!')")
    (temp_dir / "src" / "__init__.py").write_text("")
    (temp_dir / "docs" / "api.md").write_text("# API Documentation")
    (temp_dir / "tests" / "test_main.py").write_text("def test_main(): pass")
    (temp_dir / "config.json").write_text('{"name": "test"}')
    (temp_dir / ".gitignore").write_text("*.pyc\n__pycache__/")
    
    # Create files that should be excluded
    (temp_dir / "node_modules").mkdir()
    (temp_dir / "node_modules" / "package.js").write_text("// package")
    (temp_dir / "image.png").write_bytes(b"fake image data")
    (temp_dir / "app.log").write_text("log entry")
    
    yield temp_dir
    
    # Cleanup
    shutil.rmtree(temp_dir)


@pytest.fixture
def mock_github_client():
    """Create a mock GitHub client."""
    client = Mock(spec=GitHubClient)
    client.authenticate = AsyncMock(return_value=True)
    
    # Mock repository info
    from src.ingestion.base import RepositoryInfo
    repo_info = RepositoryInfo(
        url="https://github.com/test/repo",
        name="repo",
        owner="test",
        branch="main",
        commit_sha="abc123",
        is_private=False,
        description="Test repository",
        language="Python",
        size=1024,
        created_at=datetime(2023, 1, 1),
        updated_at=datetime(2023, 12, 1),
    )
    client.get_repository_info = AsyncMock(return_value=repo_info)
    
    return client


@pytest.fixture
def mock_repository_manager(temp_repo_dir):
    """Create a mock repository manager."""
    manager = Mock(spec=GitHubRepositoryManager)
    manager.clone_repository = AsyncMock(return_value=temp_repo_dir)
    manager.temp_dir = temp_repo_dir.parent  # Add temp_dir attribute

    # Mock file listing
    files = [
        "README.md",
        "src/main.py",
        "src/__init__.py",
        "docs/api.md",
        "tests/test_main.py",
        "config.json",
        ".gitignore",
        "node_modules/package.js",
        "image.png",
        "app.log",
    ]
    manager.get_file_list = AsyncMock(return_value=files)

    return manager


@pytest.fixture
def github_connector(mock_settings, mock_github_client, mock_repository_manager):
    """Create GitHubConnector with mocked dependencies."""
    file_filter = GitHubFileFilter(mock_settings)
    metadata_extractor = GitHubMetadataExtractor(mock_settings)
    
    return GitHubConnector(
        settings=mock_settings,
        github_client=mock_github_client,
        repository_manager=mock_repository_manager,
        file_filter=file_filter,
        metadata_extractor=metadata_extractor,
    )


class TestGitHubConnectorIntegration:
    """Integration test cases for GitHubConnector."""
    
    @pytest.mark.asyncio
    async def test_ingest_repository_success(self, github_connector, temp_repo_dir):
        """Test successful repository ingestion."""
        result = await github_connector.ingest_repository(
            "https://github.com/test/repo",
            branch="main"
        )
        
        # Verify result structure
        assert "repository_url" in result
        assert "branch" in result
        assert "repository_info" in result
        assert "repository_metadata" in result
        assert "total_files" in result
        assert "processed_files" in result
        assert "file_metadata" in result
        assert "ingestion_duration" in result
        assert "local_path" in result
        
        # Verify repository URL and branch
        assert result["repository_url"] == "https://github.com/test/repo"
        assert result["branch"] == "main"
        
        # Verify file processing
        assert result["total_files"] == 10  # All files found
        assert result["processed_files"] > 0  # Some files processed
        assert result["processed_files"] < result["total_files"]  # Some files filtered out
        
        # Verify file metadata structure
        file_metadata = result["file_metadata"]
        assert len(file_metadata) > 0
        
        for metadata in file_metadata:
            assert "file_path" in metadata
            assert "file_size" in metadata
            assert "file_type" in metadata
            assert "priority" in metadata
    
    @pytest.mark.asyncio
    async def test_ingest_repository_file_filtering(self, github_connector):
        """Test that file filtering works correctly during ingestion."""
        result = await github_connector.ingest_repository(
            "https://github.com/test/repo",
            branch="main"
        )
        
        # Get processed file paths
        processed_files = [fm["file_path"] for fm in result["file_metadata"]]
        
        # Should include these files
        assert "README.md" in processed_files
        assert "src/main.py" in processed_files
        assert "docs/api.md" in processed_files
        assert "config.json" in processed_files
        
        # Should exclude these files
        assert "node_modules/package.js" not in processed_files
        assert "image.png" not in processed_files
        assert "app.log" not in processed_files
    
    @pytest.mark.asyncio
    async def test_ingest_repository_priority_scoring(self, github_connector):
        """Test that priority scoring is applied correctly."""
        result = await github_connector.ingest_repository(
            "https://github.com/test/repo",
            branch="main"
        )
        
        # Find specific files and check their priorities
        file_priorities = {
            fm["file_path"]: fm["priority"] 
            for fm in result["file_metadata"]
        }
        
        # docs/ files should have higher priority
        if "docs/api.md" in file_priorities:
            assert file_priorities["docs/api.md"] > 0
        
        # README should have high priority
        if "README.md" in file_priorities:
            assert file_priorities["README.md"] > 0
        
        # Regular source files should have some priority
        if "src/main.py" in file_priorities:
            assert file_priorities["src/main.py"] >= 0
    
    @pytest.mark.asyncio
    async def test_ingest_repository_metadata_extraction(self, github_connector):
        """Test that metadata extraction works correctly."""
        result = await github_connector.ingest_repository(
            "https://github.com/test/repo",
            branch="main"
        )
        
        # Check repository metadata
        repo_metadata = result["repository_metadata"]
        assert "path" in repo_metadata
        assert "name" in repo_metadata
        assert "extracted_at" in repo_metadata
        
        # Check file metadata
        file_metadata = result["file_metadata"]
        assert len(file_metadata) > 0
        
        for metadata in file_metadata:
            assert metadata["file_path"]
            assert metadata["file_size"] >= 0
            assert metadata["file_type"]
            assert "last_modified" in metadata
    
    @pytest.mark.asyncio
    async def test_ingest_files_batch_processing(self, github_connector, temp_repo_dir):
        """Test that file batch processing works correctly."""
        # Create a list of files to process
        files = [
            "README.md",
            "src/main.py",
            "src/__init__.py",
            "docs/api.md",
            "config.json",
        ]
        
        result = await github_connector.ingest_files(files, temp_repo_dir)
        
        assert len(result) == len(files)
        
        # Verify each file was processed
        processed_paths = [fm.file_path for fm in result]
        for file_path in files:
            assert file_path in processed_paths
    
    @pytest.mark.asyncio
    async def test_ingest_repository_error_handling(self, github_connector, mock_github_client):
        """Test error handling during repository ingestion."""
        # Mock an authentication error
        mock_github_client.get_repository_info.side_effect = Exception("API Error")
        
        with pytest.raises(IngestionError) as exc_info:
            await github_connector.ingest_repository("https://github.com/test/repo")
        
        assert "Repository ingestion failed" in str(exc_info.value)
        assert exc_info.value.details["repository_url"] == "https://github.com/test/repo"
    
    @pytest.mark.asyncio
    async def test_get_ingestion_status(self, github_connector):
        """Test ingestion status tracking."""
        # Start an ingestion
        await github_connector.ingest_repository("https://github.com/test/repo")
        
        # Check status
        status = await github_connector.get_ingestion_status("https://github.com/test/repo")
        
        assert status["repository_url"] == "https://github.com/test/repo"
        assert status["status"] == "completed"
        assert "start_time" in status
        assert "end_time" in status
        assert "duration" in status
    
    @pytest.mark.asyncio
    async def test_get_ingestion_status_not_found(self, github_connector):
        """Test ingestion status for non-existent repository."""
        status = await github_connector.get_ingestion_status("https://github.com/nonexistent/repo")
        
        assert status["repository_url"] == "https://github.com/nonexistent/repo"
        assert status["status"] == "not_found"
    
    def test_get_connector_stats(self, github_connector):
        """Test getting connector statistics."""
        stats = github_connector.get_connector_stats()
        
        assert "total_ingestions" in stats
        assert "filter_stats" in stats
        assert "temp_directory" in stats
        assert "settings" in stats
        
        # Verify filter stats structure
        filter_stats = stats["filter_stats"]
        assert "max_file_size" in filter_stats
        assert "allowed_file_types" in filter_stats
        assert "exclusion_count" in filter_stats
    
    @pytest.mark.asyncio
    async def test_cleanup(self, github_connector, mock_repository_manager):
        """Test cleanup functionality."""
        # Test cleanup without specific repository
        await github_connector.cleanup()
        mock_repository_manager.cleanup.assert_called_once_with()
        
        # Reset mock
        mock_repository_manager.reset_mock()
        
        # Test cleanup with specific repository
        await github_connector.cleanup("https://github.com/test/repo")
        mock_repository_manager.cleanup.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_ingest_repository_force_refresh(self, github_connector, mock_repository_manager):
        """Test repository ingestion with force refresh."""
        await github_connector.ingest_repository(
            "https://github.com/test/repo",
            branch="main",
            force_refresh=True
        )
        
        # Verify that clone_repository was called with force_refresh=True
        mock_repository_manager.clone_repository.assert_called_once_with(
            "https://github.com/test/repo", "main", True
        )
