"""
Integration tests for the embedding pipeline.

Tests the complete embedding workflow including embedding generation,
vector storage, and integration with the chunking pipeline.
"""

import pytest
import tempfile
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime

from src.ingestion import (
    DefaultEmbeddingPipeline,
    DefaultChunkingPipeline,
    Chunk,
    ChunkType,
    ChunkContext,
    FileMetadata,
)
from src.config import Settings


@pytest.fixture
def mock_settings():
    """Create mock settings for testing."""
    settings = Mock(spec=Settings)
    settings.embedding_provider = "openai"
    settings.embedding_model = "text-embedding-3-large"
    settings.embedding_batch_size = 5
    settings.embedding_max_retries = 2
    settings.embedding_timeout = 30
    settings.embedding_pipeline_batch_size = 3
    settings.embedding_quality_check = True
    settings.embedding_cache_enabled = True
    settings.embedding_normalize = True
    settings.vector_store_provider = "chromadb"
    settings.chroma_collection_name = "test_collection"
    settings.chroma_persist_directory = str(Path(tempfile.mkdtemp()) / "test_chroma")
    settings.openai_api_key = "test-api-key"
    return settings


@pytest.fixture
def sample_chunks():
    """Create sample chunks for testing."""
    file_metadata = FileMetadata(
        file_path="test.py",
        file_size=1000,
        file_type="text",
        last_modified=datetime.now(),
        language="python"
    )
    
    chunks = []
    
    # Code function chunk
    chunks.append(Chunk(
        content="def hello_world():\n    return 'Hello, World!'",
        chunk_id="chunk_1",
        chunk_type=ChunkType.CODE_FUNCTION,
        file_metadata=file_metadata,
        start_line=1,
        end_line=2,
        context=ChunkContext(function_name="hello_world", module_name="test"),
        token_count=15,
        chunking_strategy="code"
    ))
    
    # Code class chunk
    chunks.append(Chunk(
        content="class TestClass:\n    def __init__(self):\n        pass",
        chunk_id="chunk_2",
        chunk_type=ChunkType.CODE_CLASS,
        file_metadata=file_metadata,
        start_line=4,
        end_line=6,
        context=ChunkContext(class_name="TestClass", module_name="test"),
        token_count=20,
        chunking_strategy="code"
    ))
    
    # Text chunk
    chunks.append(Chunk(
        content="This is a documentation paragraph explaining the functionality.",
        chunk_id="chunk_3",
        chunk_type=ChunkType.TEXT_PARAGRAPH,
        file_metadata=file_metadata,
        start_line=8,
        end_line=8,
        context=ChunkContext(),
        token_count=12,
        chunking_strategy="text"
    ))
    
    return chunks


class TestEmbeddingPipelineIntegration:
    """Integration test cases for the embedding pipeline."""
    
    @pytest.mark.asyncio
    async def test_complete_embedding_workflow(self, mock_settings, sample_chunks):
        """Test complete embedding workflow from chunks to vector store."""
        # Mock embedding client
        mock_embedding_client = Mock()
        mock_embedding_client.generate_embeddings = AsyncMock(return_value=[
            [0.1, 0.2, 0.3] * 1024,  # 3072 dimensions
            [0.4, 0.5, 0.6] * 1024,
            [0.7, 0.8, 0.9] * 1024,
        ])
        mock_embedding_client.get_embedding_dimension.return_value = 3072
        mock_embedding_client.get_model_info.return_value = {
            "model": "text-embedding-3-large",
            "provider": "openai",
            "dimension": 3072
        }
        
        # Mock vector store
        mock_vector_store = Mock()
        mock_vector_store.add_embeddings = AsyncMock(return_value=True)
        mock_vector_store.get_collection_info = AsyncMock(return_value={
            "name": "test_collection",
            "count": 3
        })
        
        # Create pipeline with mocked dependencies
        pipeline = DefaultEmbeddingPipeline(
            mock_settings,
            embedding_client=mock_embedding_client,
            vector_store=mock_vector_store
        )
        
        # Process chunks
        embedded_chunks = await pipeline.process_chunks(sample_chunks)
        
        # Verify embeddings were generated
        assert len(embedded_chunks) == 3
        for embedded_chunk in embedded_chunks:
            assert len(embedded_chunk.embedding) == 3072
            assert embedded_chunk.embedding_metadata.embedding_model == "text-embedding-3-large"
            assert embedded_chunk.embedding_metadata.embedding_provider == "openai"
        
        # Store embeddings
        success = await pipeline.store_embeddings(embedded_chunks)
        assert success is True
        
        # Verify vector store was called
        mock_vector_store.add_embeddings.assert_called_once()
        call_args = mock_vector_store.add_embeddings.call_args
        assert len(call_args[0][0]) == 3  # embeddings
        assert len(call_args[0][1]) == 3  # metadata
        assert len(call_args[0][2]) == 3  # ids
    
    @pytest.mark.asyncio
    async def test_embedding_caching(self, mock_settings, sample_chunks):
        """Test embedding caching functionality."""
        mock_embedding_client = Mock()
        # Mock should return one embedding for the unique content
        mock_embedding_client.generate_embeddings = AsyncMock(return_value=[
            [0.1, 0.2, 0.3] * 1024,  # One embedding for the unique content
        ])
        mock_embedding_client.get_embedding_dimension.return_value = 3072
        mock_embedding_client.get_model_info.return_value = {
            "model": "text-embedding-3-large",
            "provider": "openai",
            "dimension": 3072
        }

        mock_vector_store = Mock()
        mock_vector_store.add_embeddings = AsyncMock(return_value=True)

        pipeline = DefaultEmbeddingPipeline(
            mock_settings,
            embedding_client=mock_embedding_client,
            vector_store=mock_vector_store
        )

        # Test caching across separate batches
        chunk1 = sample_chunks[0]

        # Process first chunk to populate cache
        first_batch = await pipeline.process_chunks([chunk1])
        assert len(first_batch) == 1
        assert mock_embedding_client.generate_embeddings.call_count == 1

        # Create second chunk with same content
        chunk2 = Chunk(
            content=chunk1.content,  # Same content
            chunk_id="chunk_1_duplicate",  # Different ID
            chunk_type=chunk1.chunk_type,
            file_metadata=chunk1.file_metadata,
            start_line=chunk1.start_line,
            end_line=chunk1.end_line,
            context=chunk1.context,
            token_count=chunk1.token_count,
            chunking_strategy=chunk1.chunking_strategy
        )

        # Process second chunk (should hit cache)
        second_batch = await pipeline.process_chunks([chunk2])
        assert len(second_batch) == 1

        # Should still only have called embedding generation once due to caching
        assert mock_embedding_client.generate_embeddings.call_count == 1

        # Both chunks should have the same embedding (from cache)
        assert first_batch[0].embedding == second_batch[0].embedding

        # Check cache hit stats
        stats = await pipeline.get_pipeline_stats()
        assert stats["cache_hits"] == 1
    
    @pytest.mark.asyncio
    async def test_batch_processing(self, mock_settings, sample_chunks):
        """Test batch processing with small batch size."""
        # Set small batch size to test batching
        mock_settings.embedding_pipeline_batch_size = 2
        
        mock_embedding_client = Mock()
        # Mock will be called twice due to batch size of 2 with 3 chunks
        mock_embedding_client.generate_embeddings = AsyncMock(side_effect=[
            [[0.1, 0.2, 0.3] * 1024, [0.4, 0.5, 0.6] * 1024],  # First batch (2 chunks)
            [[0.7, 0.8, 0.9] * 1024],  # Second batch (1 chunk)
        ])
        mock_embedding_client.get_embedding_dimension.return_value = 3072
        mock_embedding_client.get_model_info.return_value = {
            "model": "text-embedding-3-large",
            "provider": "openai",
            "dimension": 3072
        }
        
        mock_vector_store = Mock()
        mock_vector_store.add_embeddings = AsyncMock(return_value=True)
        
        pipeline = DefaultEmbeddingPipeline(
            mock_settings,
            embedding_client=mock_embedding_client,
            vector_store=mock_vector_store
        )
        
        # Process chunks
        embedded_chunks = await pipeline.process_chunks(sample_chunks)
        
        # Should call embedding generation twice due to batching
        assert mock_embedding_client.generate_embeddings.call_count == 2
        assert len(embedded_chunks) == 3
    
    @pytest.mark.asyncio
    async def test_search_functionality(self, mock_settings):
        """Test search functionality."""
        mock_embedding_client = Mock()
        mock_embedding_client.generate_single_embedding = AsyncMock(return_value=[0.5, 0.5, 0.5] * 1024)
        
        mock_search_result = Mock()
        mock_search_result.similarity_score = 0.95
        mock_search_result.rank = 1
        mock_search_result.content = "def hello_world():\n    return 'Hello, World!'"
        
        mock_vector_store = Mock()
        mock_vector_store.search_similar = AsyncMock(return_value=[mock_search_result])
        
        pipeline = DefaultEmbeddingPipeline(
            mock_settings,
            embedding_client=mock_embedding_client,
            vector_store=mock_vector_store
        )
        
        # Search for similar chunks
        results = await pipeline.search_similar_chunks("hello world function", top_k=5)
        
        assert len(results) == 1
        assert results[0].similarity_score == 0.95
        assert "hello_world" in results[0].content
        
        # Verify search was called correctly
        mock_vector_store.search_similar.assert_called_once()
        call_args = mock_vector_store.search_similar.call_args
        assert call_args.kwargs["top_k"] == 5
    
    @pytest.mark.asyncio
    async def test_quality_validation(self, mock_settings, sample_chunks):
        """Test embedding quality validation."""
        mock_settings.embedding_quality_check = True
        
        mock_embedding_client = Mock()
        # Return invalid embeddings (wrong dimension)
        mock_embedding_client.generate_embeddings = AsyncMock(return_value=[
            [0.1, 0.2],  # Wrong dimension (should be 3072)
            [0.4, 0.5, 0.6] * 1024,  # Correct dimension
            [],  # Empty embedding
        ])
        mock_embedding_client.get_embedding_dimension.return_value = 3072
        mock_embedding_client.get_model_info.return_value = {
            "model": "text-embedding-3-large",
            "provider": "openai",
            "dimension": 3072
        }
        
        mock_vector_store = Mock()
        mock_vector_store.add_embeddings = AsyncMock(return_value=True)
        
        pipeline = DefaultEmbeddingPipeline(
            mock_settings,
            embedding_client=mock_embedding_client,
            vector_store=mock_vector_store
        )
        
        # Process chunks
        embedded_chunks = await pipeline.process_chunks(sample_chunks)
        
        # Should only have 1 valid embedding (middle one)
        assert len(embedded_chunks) == 1
        assert len(embedded_chunks[0].embedding) == 3072
        
        # Check stats for quality failures
        stats = await pipeline.get_pipeline_stats()
        assert stats["quality_failures"] == 2
        assert stats["failed_chunks"] == 2
    
    @pytest.mark.asyncio
    async def test_pipeline_statistics(self, mock_settings, sample_chunks):
        """Test pipeline statistics collection."""
        mock_embedding_client = Mock()
        mock_embedding_client.generate_embeddings = AsyncMock(return_value=[
            [0.1, 0.2, 0.3] * 1024,
            [0.4, 0.5, 0.6] * 1024,
            [0.7, 0.8, 0.9] * 1024,
        ])
        mock_embedding_client.get_embedding_dimension.return_value = 3072
        mock_embedding_client.get_model_info.return_value = {
            "model": "text-embedding-3-large",
            "provider": "openai",
            "dimension": 3072
        }
        mock_embedding_client.get_stats.return_value = {"total_requests": 1}
        
        mock_vector_store = Mock()
        mock_vector_store.add_embeddings = AsyncMock(return_value=True)
        mock_vector_store.get_stats.return_value = {"total_embeddings": 3}
        mock_vector_store.get_collection_info = AsyncMock(return_value={
            "name": "test_collection",
            "count": 3
        })
        
        pipeline = DefaultEmbeddingPipeline(
            mock_settings,
            embedding_client=mock_embedding_client,
            vector_store=mock_vector_store
        )
        
        # Process chunks
        embedded_chunks = await pipeline.process_chunks(sample_chunks)
        await pipeline.store_embeddings(embedded_chunks)
        
        # Get comprehensive stats
        stats = await pipeline.get_pipeline_stats()
        
        assert stats["chunks_processed"] == 3
        assert stats["embeddings_generated"] == 3
        assert stats["embeddings_stored"] == 3
        assert stats["processing_time"] > 0
        assert "embedding_client" in stats
        assert "vector_store" in stats
        assert "collection_info" in stats
    
    @pytest.mark.asyncio
    async def test_error_handling(self, mock_settings, sample_chunks):
        """Test error handling in embedding pipeline."""
        mock_embedding_client = Mock()
        mock_embedding_client.generate_embeddings = AsyncMock(side_effect=Exception("API Error"))
        mock_embedding_client.get_embedding_dimension.return_value = 3072
        mock_embedding_client.get_model_info.return_value = {
            "model": "text-embedding-3-large",
            "provider": "openai",
            "dimension": 3072
        }
        
        mock_vector_store = Mock()
        
        pipeline = DefaultEmbeddingPipeline(
            mock_settings,
            embedding_client=mock_embedding_client,
            vector_store=mock_vector_store
        )
        
        # Should raise IngestionError
        with pytest.raises(Exception):  # Will be wrapped in IngestionError
            await pipeline.process_chunks(sample_chunks)
    
    def test_cache_management(self, mock_settings):
        """Test embedding cache management."""
        pipeline = DefaultEmbeddingPipeline(mock_settings)
        
        # Add something to cache
        pipeline._embedding_cache["test_hash"] = [0.1, 0.2, 0.3]
        assert len(pipeline._embedding_cache) == 1
        
        # Clear cache
        pipeline.clear_cache()
        assert len(pipeline._embedding_cache) == 0
    
    def test_stats_reset(self, mock_settings):
        """Test statistics reset functionality."""
        pipeline = DefaultEmbeddingPipeline(mock_settings)
        
        # Modify stats
        pipeline._stats["chunks_processed"] = 10
        pipeline._stats["embeddings_generated"] = 8
        
        # Reset stats
        pipeline.reset_stats()
        
        assert pipeline._stats["chunks_processed"] == 0
        assert pipeline._stats["embeddings_generated"] == 0
